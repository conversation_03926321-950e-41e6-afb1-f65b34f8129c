use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;
use futures::future::join_all;
use log::{debug, error, info, warn};
use crate::grpc::PooledClient;


/// Write consistency levels for synchronous replication
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum WriteConsistency {
    All,    // All peer nodes must respond successfully
    Quorum, // Majority (or quorum_value) of peer nodes must respond successfully
    One,    // At least one peer node must respond successfully
}

impl WriteConsistency {
    pub fn from_string(s: &str) -> Option<Self> {
        match s.to_uppercase().as_str() {
            "ALL" => Some(WriteConsistency::All),
            "QUORUM" => Some(WriteConsistency::Quorum),
            "ONE" => Some(WriteConsistency::One),
            _ => None,
        }
    }
}

/// Write operation types for peer Redis nodes
#[derive(Debug, <PERSON><PERSON>)]
pub enum WriteOperation {
    Set { key: String, value: String },
    Delete { key: String },
    SetEx { key: String, value: String, ttl: u64 },
    SetExpiry { key: String, ttl: i64 },
    IncrBy { key: String, value: i64 },
    DecrBy { key: String, value: i64 },
    IncrByFloat { key: String, value: f64 },
    HSet { key: String, field: String, value: String },
    HIncrBy { key: String, field: String, value: i64 },
    HDecrBy { key: String, field: String, value: i64 },
    HIncrByFloat { key: String, field: String, value: f64 },
}

/// Redis connection pool for peer nodes
pub struct PeerRedisPool {
    pools: HashMap<String, deadpool_redis::Pool>,
    retry_count: u32,
    retry_delay: Duration,
    pool_size: usize,
}

impl PeerRedisPool {
    pub async fn new(peer_nodes: &[String], pool_size: usize, retry_count: u32, retry_delay_ms: u64) -> Self {
        info!("Creating peer Redis pools with size {} for {} nodes", pool_size, peer_nodes.len());

        let mut pools = HashMap::new();
        let retry_delay = Duration::from_millis(retry_delay_ms);

        for node_url in peer_nodes {
            info!("Creating Redis pool for peer node: {}", node_url);

            // Create Redis pool configuration
            let config = deadpool_redis::Config::from_url(node_url);
            let pool = config.create_pool(Some(deadpool_redis::Runtime::Tokio1))
                .map_err(|e| {
                    error!("Failed to create Redis pool for peer node {}: {}", node_url, e);
                    e
                });

            match pool {
                Ok(pool) => {
                    pools.insert(node_url.clone(), pool);
                    info!("Successfully created Redis pool for peer node: {}", node_url);
                }
                Err(e) => {
                    error!("Failed to create Redis pool for peer node {}: {}", node_url, e);
                }
            }
        }

        Self {
            pools,
            retry_count,
            retry_delay,
            pool_size,
        }
    }

    pub async fn execute_write_operation(&self, node_url: &str, operation: &WriteOperation) -> bool {
        if let Some(pool) = self.pools.get(node_url) {
            for attempt in 0..=self.retry_count {
                match pool.get().await {
                    Ok(mut conn) => {
                        let result = match operation {
                            WriteOperation::Set { key, value } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.set::<_, _, ()>(key, value).await
                            }
                            WriteOperation::Delete { key } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.del::<_, ()>(key).await
                            }
                            WriteOperation::SetEx { key, value, ttl } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.set_ex::<_, _, ()>(key, value, *ttl).await
                            }
                            WriteOperation::SetExpiry { key, ttl } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.expire::<_, ()>(key, *ttl).await
                            }
                            WriteOperation::IncrBy { key, value } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.incr::<_, _, ()>(key, *value).await
                            }
                            WriteOperation::DecrBy { key, value } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.decr::<_, _, ()>(key, *value).await
                            }
                            WriteOperation::IncrByFloat { key, value } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.incr::<_, _, ()>(key, *value).await
                            }
                            WriteOperation::HSet { key, field, value } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.hset::<_, _, _, ()>(key, field, value).await
                            }
                            WriteOperation::HIncrBy { key, field, value } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.hincr::<_, _, _, ()>(key, field, *value).await
                            }
                            WriteOperation::HDecrBy { key, field, value } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.hincr::<_, _, _, ()>(key, field, -*value).await
                            }
                            WriteOperation::HIncrByFloat { key, field, value } => {
                                use deadpool_redis::redis::AsyncCommands;
                                conn.hincr::<_, _, _, ()>(key, field, *value).await
                            }
                        };

                        match result {
                            Ok(_) => {
                                if attempt > 0 {
                                    debug!("Write operation succeeded to peer {} after {} retries", node_url, attempt);
                                }
                                return true;
                            }
                            Err(e) => {
                                if attempt < self.retry_count {
                                    warn!("Write operation failed to peer {} (attempt {}/{}): {}. Retrying...",
                                          node_url, attempt + 1, self.retry_count + 1, e);
                                    sleep(self.retry_delay).await;
                                } else {
                                    error!("Write operation failed to peer {} after {} attempts: {}",
                                           node_url, self.retry_count + 1, e);
                                }
                            }
                        }
                    }
                    Err(e) => {
                        if attempt < self.retry_count {
                            warn!("Failed to get connection to peer {} (attempt {}/{}): {}. Retrying...",
                                  node_url, attempt + 1, self.retry_count + 1, e);
                            sleep(self.retry_delay).await;
                        } else {
                            error!("Failed to get connection to peer {} after {} attempts: {}",
                                   node_url, self.retry_count + 1, e);
                        }
                    }
                }
            }
        } else {
            error!("No Redis pool found for peer node: {}", node_url);
        }
        false
    }

    /// Get pool statistics for monitoring
    pub fn get_pool_stats(&self) -> HashMap<String, (usize, usize)> {
        let mut stats = HashMap::new();
        for (node_url, pool) in &self.pools {
            let status = pool.status();
            stats.insert(
                node_url.clone(),
                (status.size, self.pool_size) // (current_size, max_size)
            );
        }
        stats
    }

    /// Get the configured pool size
    pub fn get_pool_size(&self) -> usize {
        self.pool_size
    }

    /// Ping a specific Redis node to keep connection alive
    pub async fn ping_node(&self, node_url: &str) -> bool {
        if let Some(pool) = self.pools.get(node_url) {
            for attempt in 0..=self.retry_count {
                match pool.get().await {
                    Ok(mut conn) => {
                        match deadpool_redis::redis::cmd("PING").query_async::<String>(&mut conn).await {
                            Ok(_) => {
                                if attempt > 0 {
                                    debug!("Ping successful to peer {} after {} retries", node_url, attempt);
                                }
                                return true;
                            }
                            Err(e) => {
                                if attempt < self.retry_count {
                                    warn!("Ping failed to peer {} (attempt {}/{}): {}. Retrying...",
                                          node_url, attempt + 1, self.retry_count + 1, e);
                                    sleep(self.retry_delay).await;
                                } else {
                                    error!("Ping failed to peer {} after {} attempts: {}",
                                           node_url, self.retry_count + 1, e);
                                }
                            }
                        }
                    }
                    Err(e) => {
                        if attempt < self.retry_count {
                            warn!("Failed to get connection to peer {} (attempt {}/{}): {}. Retrying...",
                                  node_url, attempt + 1, self.retry_count + 1, e);
                            sleep(self.retry_delay).await;
                        } else {
                            error!("Failed to get connection to peer {} after {} attempts: {}",
                                   node_url, self.retry_count + 1, e);
                        }
                    }
                }
            }
        } else {
            error!("No Redis pool found for peer node: {}", node_url);
        }
        false
    }
}

/// Write consistency checker for synchronous replication
pub struct WriteConsistencyChecker {
    peer_redis_pool: Arc<PeerRedisPool>,
    write_consistency: WriteConsistency,
    quorum_value: usize,
}

impl WriteConsistencyChecker {
    pub async fn new(
        peer_nodes: &[String],
        write_consistency: WriteConsistency,
        quorum_value: usize,
        pool_size: usize,
        retry_count: u32,
    ) -> Self {
        let peer_redis_pool = Arc::new(
            PeerRedisPool::new(peer_nodes, pool_size, retry_count, 100).await
        );

        Self {
            peer_redis_pool,
            write_consistency,
            quorum_value,
        }
    }

    /// Execute write operation on peer Redis nodes and check consistency
    pub async fn execute_write_with_consistency(&self, peer_nodes: &[String], operation: WriteOperation) -> bool {
        if peer_nodes.is_empty() {
            return true; // No peers to write to, consider it successful
        }

        // Calculate required successes upfront
        let required_successes = match self.write_consistency {
            WriteConsistency::All => peer_nodes.len(),
            WriteConsistency::Quorum => self.quorum_value.min(peer_nodes.len()),
            WriteConsistency::One => 1,
        };

        // For ALL consistency, we must try all nodes
        if matches!(self.write_consistency, WriteConsistency::All) {
            return self.execute_write_all_nodes(peer_nodes, operation, required_successes).await;
        }

        // For QUORUM and ONE, use early termination for better performance
        self.execute_write_with_early_termination(peer_nodes, operation, required_successes).await
    }

    /// Execute write to all nodes (for ALL consistency)
    async fn execute_write_all_nodes(&self, peer_nodes: &[String], operation: WriteOperation, required_successes: usize) -> bool {
        let mut futures = Vec::with_capacity(peer_nodes.len());

        for node_url in peer_nodes {
            let pool = self.peer_redis_pool.clone();
            let op = operation.clone();
            let node_url = node_url.clone();

            futures.push(tokio::spawn(async move {
                pool.execute_write_operation(&node_url, &op).await
            }));
        }

        let results = join_all(futures).await;
        let successful_count = results.into_iter()
            .filter_map(|r| r.ok())
            .filter(|&success| success)
            .count();

        let success = successful_count >= required_successes;
        if success {
            info!("Write consistency met: {}/{} peer nodes succeeded", successful_count, peer_nodes.len());
        } else {
            error!("Write consistency failed: {}/{} peer nodes succeeded (required: {})",
                   successful_count, peer_nodes.len(), required_successes);
        }

        success
    }

    /// Execute write with early termination (for QUORUM and ONE consistency)
    async fn execute_write_with_early_termination(&self, peer_nodes: &[String], operation: WriteOperation, required_successes: usize) -> bool {
        use std::sync::atomic::{AtomicUsize, Ordering};
        use tokio::sync::mpsc;

        let success_count = Arc::new(AtomicUsize::new(0));
        let failure_count = Arc::new(AtomicUsize::new(0));
        let (tx, mut rx) = mpsc::channel(peer_nodes.len());

        // Start write operations to all nodes
        for node_url in peer_nodes {
            let pool = self.peer_redis_pool.clone();
            let op = operation.clone();
            let node_url = node_url.clone();
            let tx = tx.clone();
            let success_count = success_count.clone();
            let failure_count = failure_count.clone();

            tokio::spawn(async move {
                let result = pool.execute_write_operation(&node_url, &op).await;
                if result {
                    success_count.fetch_add(1, Ordering::Relaxed);
                } else {
                    failure_count.fetch_add(1, Ordering::Relaxed);
                }
                let _ = tx.send((node_url, result)).await;
            });
        }

        // Drop the original sender so the channel can close when all tasks complete
        drop(tx);

        let total_nodes = peer_nodes.len();
        let mut completed_count = 0;

        // Wait for results with early termination
        while let Some((_node_url, _result)) = rx.recv().await {
            completed_count += 1;

            let current_successes = success_count.load(Ordering::Relaxed);
            let _current_failures = failure_count.load(Ordering::Relaxed);

            // Early success: we have enough successes
            if current_successes >= required_successes {
                info!("Write consistency met early: {}/{} peer nodes succeeded (required: {})",
                      current_successes, total_nodes, required_successes);
                return true;
            }

            // Early failure: even if all remaining nodes succeed, we can't meet the requirement
            let remaining_nodes = total_nodes - completed_count;
            if current_successes + remaining_nodes < required_successes {
                error!("Write consistency failed early: {}/{} peer nodes succeeded, {} remaining (required: {})",
                       current_successes, total_nodes, remaining_nodes, required_successes);
                return false;
            }
        }

        // All operations completed, check final result
        let final_successes = success_count.load(Ordering::Relaxed);
        let success = final_successes >= required_successes;

        if success {
            info!("Write consistency met: {}/{} peer nodes succeeded (required: {})",
                  final_successes, total_nodes, required_successes);
        } else {
            error!("Write consistency failed: {}/{} peer nodes succeeded (required: {})",
                   final_successes, total_nodes, required_successes);
        }

        success
    }

    /// Get pool statistics for all peer nodes
    pub fn get_pool_stats(&self) -> HashMap<String, (usize, usize)> {
        self.peer_redis_pool.get_pool_stats()
    }

    /// Get the configured pool size
    pub fn get_pool_size(&self) -> usize {
        self.peer_redis_pool.get_pool_size()
    }
}

/// Site replication manager for cross-site data replication
pub struct SiteReplicationManager {
    primary_node: String,
    failover_node: String,
    primary_pool: Option<PooledClient>,
    failover_pool: Option<PooledClient>,
    primary_available: bool,
    failover_available: bool,
    retry_count: u32,
    timeout: Duration,
    last_health_check: std::time::Instant,
    health_check_interval: Duration,
}

impl SiteReplicationManager {
    pub async fn new(
        primary_node: String,
        failover_node: String,
        pool_size: usize,
        retry_count: u32,
        timeout_ms: u64,
    ) -> Self {
        info!("Creating site replication manager with primary: {}, failover: {}", primary_node, failover_node);

        // Create connection pools for both nodes
        let primary_pool = PooledClient::new(pool_size, &primary_node).await.ok();
        let failover_pool = PooledClient::new(pool_size, &failover_node).await.ok();

        Self {
            primary_node,
            failover_node,
            primary_pool,
            failover_pool,
            primary_available: true,
            failover_available: true,
            retry_count,
            timeout: Duration::from_millis(timeout_ms),
            last_health_check: std::time::Instant::now(),
            health_check_interval: Duration::from_secs(30),
        }
    }

    /// Get an available node for replication
    fn get_available_node(&self) -> Option<String> {
        if self.primary_available {
            Some(self.primary_node.clone())
        } else if self.failover_available {
            Some(self.failover_node.clone())
        } else {
            None
        }
    }

    /// Mark a node as unavailable
    pub fn mark_node_unavailable(&mut self, node: &str) {
        debug!("Marking site node as unavailable: {}", node);
        if node == self.primary_node {
            self.primary_available = false;
        } else if node == self.failover_node {
            self.failover_available = false;
        }
    }

    /// Mark a node as available
    pub fn mark_node_available(&mut self, node: &str) {
        debug!("Marking site node as available: {}", node);
        if node == self.primary_node {
            self.primary_available = true;
        } else if node == self.failover_node {
            self.failover_available = true;
        }
    }

    /// Replicate write operations to other sites
    pub async fn replicate_to_sites(&mut self, operations: Vec<WriteOperation>) -> bool {
        if operations.is_empty() {
            return true;
        }

        // Check if we need to refresh node availability
        self.refresh_node_availability_if_needed().await;

        let available_node = self.get_available_node();
        if available_node.is_none() {
            error!("No available site nodes for replication");
            return false;
        }

        let node = available_node.unwrap();
        info!("Replicating {} operations to site node: {}", operations.len(), node);

        // Try to replicate to the available node
        if self.replicate_to_single_site(&node, &operations).await {
            self.mark_node_available(&node);
            info!("Site replication succeeded to node: {}", node);
            true
        } else {
            self.mark_node_unavailable(&node);
            error!("Site replication failed to node: {}", node);

            // Try the other node if available
            if let Some(fallback_node) = self.get_available_node() {
                if fallback_node != node {
                    warn!("Trying fallback site node: {}", fallback_node);
                    if self.replicate_to_single_site(&fallback_node, &operations).await {
                        self.mark_node_available(&fallback_node);
                        info!("Site replication succeeded to fallback node: {}", fallback_node);
                        return true;
                    } else {
                        self.mark_node_unavailable(&fallback_node);
                        error!("Site replication failed to fallback node: {}", fallback_node);
                    }
                }
            }
            false
        }
    }

    /// Replicate operations to a single site node
    async fn replicate_to_single_site(&mut self, node_url: &str, operations: &[WriteOperation]) -> bool {
        info!("Replicating {} operations to site node: {}", operations.len(), node_url);

        for attempt in 0..=self.retry_count {
            match self.send_operations_to_site(node_url, operations).await {
                Ok(_) => {
                    if attempt > 0 {
                        debug!("Site replication succeeded to {} after {} retries", node_url, attempt);
                    }
                    return true;
                }
                Err(e) => {
                    if attempt < self.retry_count {
                        warn!("Site replication failed to {} (attempt {}/{}): {}. Retrying...",
                              node_url, attempt + 1, self.retry_count + 1, e);
                        sleep(Duration::from_millis(100 * (attempt + 1) as u64)).await;
                    } else {
                        error!("Site replication failed to {} after {} attempts: {}",
                               node_url, self.retry_count + 1, e);
                    }
                }
            }
        }

        false
    }

    /// Send operations to a site node via gRPC using connection pools
    async fn send_operations_to_site(&mut self, node_url: &str, operations: &[WriteOperation]) -> Result<(), String> {
        use tonic::Request;

        // Get client from connection pool based on the node
        let mut client = if node_url == self.primary_node {
            if let Some(ref mut pool) = self.primary_pool {
                pool.next_client()
            } else {
                return Err(format!("No connection pool available for primary node: {}", node_url));
            }
        } else if node_url == self.failover_node {
            if let Some(ref mut pool) = self.failover_pool {
                pool.next_client()
            } else {
                return Err(format!("No connection pool available for failover node: {}", node_url));
            }
        } else {
            return Err(format!("Unknown site node: {}", node_url));
        };

        // Import the generated gRPC client types
        use crate::grpc::rustycluster::{
            SetRequest, DeleteRequest, SetExRequest, SetExpiryRequest,
            IncrByRequest, DecrByRequest, IncrByFloatRequest,
            HSetRequest, HIncrByRequest, HDecrByRequest, HIncrByFloatRequest
        };

        // Convert WriteOperations to gRPC requests and send them
        for operation in operations {
            let result = match operation {
                WriteOperation::Set { key, value } => {
                    let request = Request::new(SetRequest {
                        key: key.clone(),
                        value: value.clone(),
                        skip_replication: false, // Allow replication at target site
                        skip_site_replication: true, // Prevent site replication loops
                    });
                    client.set(request).await.map(|_| ())
                }
                WriteOperation::Delete { key } => {
                    let request = Request::new(DeleteRequest {
                        key: key.clone(),
                        skip_replication: false, // Allow replication at target site
                        skip_site_replication: true, // Prevent site replication loops
                    });
                    client.delete(request).await.map(|_| ())
                }
                WriteOperation::SetEx { key, value, ttl } => {
                    let request = Request::new(SetExRequest {
                        key: key.clone(),
                        value: value.clone(),
                        ttl: *ttl,
                        skip_replication: false, // Allow replication at target site
                        skip_site_replication: true, // Prevent site replication loops
                    });
                    client.set_ex(request).await.map(|_| ())
                }
                WriteOperation::SetExpiry { key, ttl } => {
                    let request = Request::new(SetExpiryRequest {
                        key: key.clone(),
                        ttl: *ttl,
                        skip_replication: false, // Allow replication at target site
                        skip_site_replication: true, // Prevent site replication loops
                    });
                    client.set_expiry(request).await.map(|_| ())
                }
                WriteOperation::IncrBy { key, value } => {
                    let request = Request::new(IncrByRequest {
                        key: key.clone(),
                        value: *value,
                        skip_replication: false, // Allow replication at target site
                        skip_site_replication: true, // Prevent site replication loops
                    });
                    client.incr_by(request).await.map(|_| ())
                }
                WriteOperation::DecrBy { key, value } => {
                    let request = Request::new(DecrByRequest {
                        key: key.clone(),
                        value: *value,
                        skip_replication: false, // Allow replication at target site
                        skip_site_replication: true, // Prevent site replication loops
                    });
                    client.decr_by(request).await.map(|_| ())
                }
                WriteOperation::IncrByFloat { key, value } => {
                    let request = Request::new(IncrByFloatRequest {
                        key: key.clone(),
                        value: *value,
                        skip_replication: false, // Allow replication at target site
                        skip_site_replication: true, // Prevent site replication loops
                    });
                    client.incr_by_float(request).await.map(|_| ())
                }
                WriteOperation::HSet { key, field, value } => {
                    let request = Request::new(HSetRequest {
                        key: key.clone(),
                        field: field.clone(),
                        value: value.clone(),
                        skip_replication: false, // Allow replication at target site
                        skip_site_replication: true, // Prevent site replication loops
                    });
                    client.h_set(request).await.map(|_| ())
                }
                WriteOperation::HIncrBy { key, field, value } => {
                    let request = Request::new(HIncrByRequest {
                        key: key.clone(),
                        field: field.clone(),
                        value: *value,
                        skip_replication: false, // Allow replication at target site
                        skip_site_replication: true, // Prevent site replication loops
                    });
                    client.h_incr_by(request).await.map(|_| ())
                }
                WriteOperation::HDecrBy { key, field, value } => {
                    let request = Request::new(HDecrByRequest {
                        key: key.clone(),
                        field: field.clone(),
                        value: *value,
                        skip_replication: false, // Allow replication at target site
                        skip_site_replication: true, // Prevent site replication loops
                    });
                    client.h_decr_by(request).await.map(|_| ())
                }
                WriteOperation::HIncrByFloat { key, field, value } => {
                    let request = Request::new(HIncrByFloatRequest {
                        key: key.clone(),
                        field: field.clone(),
                        value: *value,
                        skip_replication: false, // Allow replication at target site
                        skip_site_replication: true, // Prevent site replication loops
                    });
                    client.h_incr_by_float(request).await.map(|_| ())
                }
            };

            if let Err(e) = result {
                return Err(format!("Failed to send operation to {}: {}", node_url, e));
            }
        }

        Ok(())
    }

    /// Refresh node availability if needed
    async fn refresh_node_availability_if_needed(&mut self) {
        if self.last_health_check.elapsed() >= self.health_check_interval {
            debug!("Refreshing site node availability");
            // For now, we'll keep the current availability status
            // In a real implementation, we would ping the nodes to check their health
            self.last_health_check = std::time::Instant::now();
        }
    }
}
