#!/usr/bin/env python3
"""
Simple test script to verify Rusty<PERSON><PERSON> authentication functionality.
This script demonstrates the authentication flow:
1. Connect to RustyCluster
2. Authenticate with username/password
3. Use the session token for subsequent operations

Requirements:
- pip install grpcio grpcio-tools
- Generate Python gRPC stubs from rustycluster.proto
"""

import grpc
import sys
import os

# Add the generated protobuf files to the path
# You need to generate these first with:
# python -m grpc_tools.protoc -I./proto --python_out=. --grpc_python_out=. proto/rustycluster.proto

try:
    import rustycluster_pb2
    import rustycluster_pb2_grpc
except ImportError:
    print("Error: gRPC Python stubs not found.")
    print("Please generate them first with:")
    print("python -m grpc_tools.protoc -I./proto --python_out=. --grpc_python_out=. proto/rustycluster.proto")
    sys.exit(1)

def test_authentication():
    """Test the authentication flow"""
    
    # Connect to RustyCluster
    channel = grpc.insecure_channel('localhost:50051')
    stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
    
    try:
        # Test 1: Authenticate with correct credentials
        print("Testing authentication with correct credentials...")
        auth_request = rustycluster_pb2.AuthenticateRequest(
            username="testuser",
            password="testpass"
        )
        
        auth_response = stub.Authenticate(auth_request)
        
        if auth_response.success:
            print(f"✓ Authentication successful! Session token: {auth_response.session_token[:20]}...")
            session_token = auth_response.session_token
        else:
            print(f"✗ Authentication failed: {auth_response.message}")
            return False
        
        # Test 2: Use session token for a ping operation
        print("Testing ping with session token...")
        metadata = [('authorization', f'Bearer {session_token}')]
        
        ping_request = rustycluster_pb2.PingRequest()
        ping_response = stub.Ping(ping_request, metadata=metadata)
        
        if ping_response.success:
            print(f"✓ Ping successful: {ping_response.message}")
        else:
            print("✗ Ping failed")
            return False
        
        # Test 3: Try to authenticate with wrong credentials
        print("Testing authentication with wrong credentials...")
        wrong_auth_request = rustycluster_pb2.AuthenticateRequest(
            username="wronguser",
            password="wrongpass"
        )
        
        wrong_auth_response = stub.Authenticate(wrong_auth_request)
        
        if not wrong_auth_response.success:
            print(f"✓ Authentication correctly failed: {wrong_auth_response.message}")
        else:
            print("✗ Authentication should have failed but didn't")
            return False
        
        print("\n✓ All authentication tests passed!")
        return True
        
    except grpc.RpcError as e:
        print(f"✗ gRPC error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False
    finally:
        channel.close()

def test_without_authentication():
    """Test operations without authentication (should work when auth is disabled)"""
    
    channel = grpc.insecure_channel('localhost:50051')
    stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
    
    try:
        print("Testing ping without authentication...")
        ping_request = rustycluster_pb2.PingRequest()
        ping_response = stub.Ping(ping_request)
        
        if ping_response.success:
            print(f"✓ Ping without auth successful: {ping_response.message}")
            return True
        else:
            print("✗ Ping without auth failed")
            return False
            
    except grpc.RpcError as e:
        print(f"✗ gRPC error: {e}")
        return False
    finally:
        channel.close()

if __name__ == "__main__":
    print("RustyCluster Authentication Test")
    print("=" * 40)
    
    # First test without authentication (for servers with auth disabled)
    print("\n1. Testing without authentication (works if auth_enabled=false):")
    test_without_authentication()
    
    # Then test with authentication (for servers with auth enabled)
    print("\n2. Testing with authentication (works if auth_enabled=true):")
    test_authentication()
    
    print("\nTest completed!")
