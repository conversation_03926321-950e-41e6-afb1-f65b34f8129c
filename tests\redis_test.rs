use rustycluster::redis_lib::RedisClient;
use std::env;

#[tokio::test]
#[ignore] // Ignore by default since it requires a running Redis server
async fn test_redis_operations() {
    // Check if Redis requires authentication
    let redis_password = std::env::var("REDIS_PASSWORD").ok();

    // Create a Redis client with or without authentication
    let has_auth = redis_password.is_some();
    let redis_url = match &redis_password {
        Some(password) => format!("redis://:{password}@localhost:6379", password = password),
        None => "redis://localhost:6379".to_string(),
    };

    println!("Connecting to Red<PERSON> with{} authentication", if has_auth { "" } else { "out" });
    let redis_client = match RedisClient::new(&redis_url, 10) {
        Ok(client) => client,
        Err(e) => {
            println!("Skipping test: Failed to create Redis client: {}", e);
            return;
        }
    };

    // Test ping
    match redis_client.ping().await {
        Ok(_) => println!("✅ Ping successful"),
        Err(e) => {
            println!("Skipping test: Failed to ping Redis: {}", e);
            return;
        }
    };

    // Test set and get
    let key = "test_key";
    let value = "test_value";
    if let Err(e) = redis_client.set(key, value).await {
        println!("Skipping test: Failed to set key: {}", e);
        return;
    }

    match redis_client.get(key).await {
        Ok(retrieved_value) => {
            assert_eq!(retrieved_value, value);
            println!("✅ Set and Get successful");
        },
        Err(e) => {
            println!("Skipping test: Failed to get key: {}", e);
            return;
        }
    }

    // Test setex and expiry
    let key_with_ttl = "test_key_ttl";
    if let Err(e) = redis_client.setex(key_with_ttl, value, 10).await {
        println!("Skipping test: Failed to set key with TTL: {}", e);
        return;
    }

    match redis_client.get(key_with_ttl).await {
        Ok(retrieved_value) => {
            assert_eq!(retrieved_value, value);
            println!("✅ SetEx successful");
        },
        Err(e) => {
            println!("Skipping test: Failed to get key with TTL: {}", e);
            return;
        }
    }

    // Test delete
    if let Err(e) = redis_client.del(key).await {
        println!("Skipping test: Failed to delete key: {}", e);
        return;
    }

    match redis_client.get(key).await {
        Ok(_) => {
            println!("Skipping test: Key should have been deleted but still exists");
            return;
        },
        Err(_) => {
            println!("✅ Delete successful");
        }
    }

    // Test hash operations
    let hash_key = "test_hash";
    let field = "field1";
    if let Err(e) = redis_client.hset(hash_key, field, value).await {
        println!("Skipping test: Failed to set hash field: {}", e);
        return;
    }

    match redis_client.hget(hash_key, field).await {
        Ok(retrieved_value) => {
            assert_eq!(retrieved_value, value);
            println!("✅ Hash operations successful");
        },
        Err(e) => {
            println!("Skipping test: Failed to get hash field: {}", e);
            return;
        }
    }

    // Test batch operations
    let batch_keys = vec![
        ("batch_key1".to_string(), "value1".to_string()),
        ("batch_key2".to_string(), "value2".to_string()),
        ("batch_key3".to_string(), "value3".to_string()),
    ];

    if let Err(e) = redis_client.batch_set(&batch_keys).await {
        println!("Skipping test: Failed to batch set: {}", e);
        return;
    }
    println!("✅ Batch set successful");

    // Clean up
    let keys_to_delete = vec![
        "batch_key1".to_string(),
        "batch_key2".to_string(),
        "batch_key3".to_string(),
        key_with_ttl.to_string(),
        hash_key.to_string(),
    ];

    if let Err(e) = redis_client.batch_del(&keys_to_delete).await {
        println!("Warning: Failed to clean up test keys: {}", e);
        // Continue anyway since this is just cleanup
    } else {
        println!("✅ Batch delete successful");
    }

    println!("All Redis tests passed!");
}

// This test is conditionally compiled and only runs when the REDIS_AUTH_TEST environment variable is set
// along with REDIS_USERNAME and/or REDIS_PASSWORD environment variables
#[tokio::test]
#[ignore] // Ignored by default, run with: cargo test -- --ignored redis_auth
async fn test_redis_auth() {
    // Check if we should run this test
    let redis_username = std::env::var("REDIS_USERNAME").ok();
    let redis_password = std::env::var("REDIS_PASSWORD").ok();

    if redis_username.is_none() && redis_password.is_none() {
        println!("Skipping Redis auth test: Neither REDIS_USERNAME nor REDIS_PASSWORD environment variables are set");
        return;
    }

    // Create a Redis client with authentication based on available credentials
    let redis_url = match (redis_username, redis_password) {
        (Some(username), Some(password)) => {
            // Both username and password provided
            println!("Testing Redis authentication with username and password");
            format!("redis://{username}:{password}@localhost:6379")
        },
        (None, Some(password)) => {
            // Only password provided
            println!("Testing Redis authentication with password only");
            format!("redis://:{password}@localhost:6379")
        },
        (Some(username), None) => {
            // Only username provided (will be treated as password)
            println!("Testing Redis authentication with username only (treated as password)");
            format!("redis://{username}@localhost:6379")
        },
        _ => unreachable!(), // We already checked that at least one is Some
    };

    println!("Testing Redis authentication (credentials hidden)");

    let redis_client = match RedisClient::new(&redis_url, 10) {
        Ok(client) => client,
        Err(e) => {
            panic!("Failed to create Redis client with authentication: {}", e);
        }
    };

    // Test ping to verify connection works
    match redis_client.ping().await {
        Ok(_) => println!("✅ Authenticated ping successful"),
        Err(e) => panic!("Failed to ping Redis with authentication: {}", e),
    }

    // Test basic operations with authentication
    let key = "auth_test_key";
    let value = "auth_test_value";

    redis_client.set(key, value).await.expect("Failed to set key with authentication");
    let retrieved_value = redis_client.get(key).await.expect("Failed to get key with authentication");
    assert_eq!(retrieved_value, value);
    println!("✅ Authenticated Set and Get successful");

    // Clean up
    redis_client.del(key).await.expect("Failed to delete key with authentication");
    println!("✅ Authenticated Delete successful");

    println!("All Redis authentication tests passed!");
}
