#!/usr/bin/env python3
"""
Load testing script for Rusty<PERSON>luster with authentication support.
This script handles the authentication flow and then performs load testing.

Requirements:
- pip install grpcio grpcio-tools asyncio aiogrpc
"""

import grpc
import asyncio
import time
import json
import sys
from concurrent.futures import ThreadPoolExecutor
import threading

try:
    import rustycluster_pb2
    import rustycluster_pb2_grpc
except ImportError:
    print("Error: gRPC Python stubs not found.")
    print("Please generate them first with:")
    print("python -m grpc_tools.protoc -I./proto --python_out=. --grpc_python_out=. proto/rustycluster.proto")
    sys.exit(1)

class AuthenticatedLoadTester:
    def __init__(self, host="localhost", port=50051, username="testuser", password="testpass"):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.session_token = None
        self.channel = None
        self.stub = None
        
    def authenticate(self):
        """Authenticate and get session token"""
        self.channel = grpc.insecure_channel(f'{self.host}:{self.port}')
        self.stub = rustycluster_pb2_grpc.KeyValueServiceStub(self.channel)
        
        auth_request = rustycluster_pb2.AuthenticateRequest(
            username=self.username,
            password=self.password
        )
        
        auth_response = self.stub.Authenticate(auth_request)
        
        if auth_response.success:
            self.session_token = auth_response.session_token
            print(f"✓ Authenticated successfully")
            return True
        else:
            print(f"✗ Authentication failed: {auth_response.message}")
            return False
    
    def create_authenticated_stub(self):
        """Create a stub with authentication metadata"""
        metadata = [('authorization', f'Bearer {self.session_token}')]
        return self.stub, metadata
    
    def set_operation(self, key, value):
        """Perform a Set operation with authentication"""
        stub, metadata = self.create_authenticated_stub()
        
        request = rustycluster_pb2.SetRequest(
            key=key,
            value=value,
            skip_replication=False
        )
        
        try:
            response = stub.Set(request, metadata=metadata)
            return response.success
        except grpc.RpcError as e:
            print(f"Set operation failed: {e}")
            return False
    
    def load_test(self, num_operations=1000, concurrency=10, rps=100):
        """Perform load testing with authentication"""
        if not self.authenticate():
            return
        
        print(f"Starting load test: {num_operations} operations, {concurrency} concurrent, {rps} RPS")
        
        # Calculate delay between requests to achieve target RPS
        delay_between_requests = 1.0 / rps if rps > 0 else 0
        
        successful_ops = 0
        failed_ops = 0
        start_time = time.time()
        
        def worker(worker_id, operations_per_worker):
            nonlocal successful_ops, failed_ops
            worker_successful = 0
            worker_failed = 0
            
            for i in range(operations_per_worker):
                key = f"loadtest_key_{worker_id}_{i}"
                value = f"loadtest_value_{worker_id}_{i}_{int(time.time())}"
                
                if self.set_operation(key, value):
                    worker_successful += 1
                else:
                    worker_failed += 1
                
                # Rate limiting
                if delay_between_requests > 0:
                    time.sleep(delay_between_requests)
            
            # Thread-safe update of counters
            with threading.Lock():
                nonlocal successful_ops, failed_ops
                successful_ops += worker_successful
                failed_ops += worker_failed
        
        # Calculate operations per worker
        operations_per_worker = num_operations // concurrency
        remaining_operations = num_operations % concurrency
        
        # Start worker threads
        with ThreadPoolExecutor(max_workers=concurrency) as executor:
            futures = []
            
            for worker_id in range(concurrency):
                ops_for_this_worker = operations_per_worker
                if worker_id < remaining_operations:
                    ops_for_this_worker += 1
                
                future = executor.submit(worker, worker_id, ops_for_this_worker)
                futures.append(future)
            
            # Wait for all workers to complete
            for future in futures:
                future.result()
        
        end_time = time.time()
        duration = end_time - start_time
        actual_rps = num_operations / duration
        
        print(f"\nLoad test completed:")
        print(f"Duration: {duration:.2f} seconds")
        print(f"Successful operations: {successful_ops}")
        print(f"Failed operations: {failed_ops}")
        print(f"Actual RPS: {actual_rps:.2f}")
        print(f"Success rate: {(successful_ops / num_operations) * 100:.2f}%")
    
    def cleanup(self):
        """Clean up resources"""
        if self.channel:
            self.channel.close()

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Load test RustyCluster with authentication')
    parser.add_argument('--host', default='localhost', help='RustyCluster host')
    parser.add_argument('--port', type=int, default=50051, help='RustyCluster port')
    parser.add_argument('--username', default='testuser', help='Username')
    parser.add_argument('--password', default='testpass', help='Password')
    parser.add_argument('--operations', '-n', type=int, default=1000, help='Number of operations')
    parser.add_argument('--concurrency', '-c', type=int, default=10, help='Concurrent connections')
    parser.add_argument('--rps', type=int, default=100, help='Requests per second (0 = unlimited)')
    
    args = parser.parse_args()
    
    tester = AuthenticatedLoadTester(args.host, args.port, args.username, args.password)
    
    try:
        tester.load_test(args.operations, args.concurrency, args.rps)
    finally:
        tester.cleanup()

if __name__ == "__main__":
    main()
