#!/usr/bin/env python3
"""
Optimized load testing script for RustyCluster with authentication support.
This version is designed to achieve 10000+ RPS with proper connection pooling,
async operations, and optimized threading.

Requirements:
- pip install grpcio grpcio-tools asyncio aiofiles
"""

import grpc
import asyncio
import time
import json
import sys
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue
import random
import string

try:
    import rustycluster_pb2
    import rustycluster_pb2_grpc
except ImportError:
    print("Error: gRPC Python stubs not found.")
    print("Please generate them first with:")
    print("python -m grpc_tools.protoc -I./proto --python_out=. --grpc_python_out=. proto/rustycluster.proto")
    sys.exit(1)

class OptimizedLoadTester:
    def __init__(self, host="localhost", port=50051, username="testuser", password="testpass"):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.session_token = None
        self.channels = []
        self.stubs = []
        self.results_queue = queue.Queue()
        
    def authenticate(self):
        """Authenticate and get session token"""
        channel = grpc.insecure_channel(f'{self.host}:{self.port}', options=[
            ('grpc.keepalive_time_ms', 10000),
            ('grpc.keepalive_timeout_ms', 5000),
            ('grpc.keepalive_permit_without_calls', True),
            ('grpc.http2.max_pings_without_data', 0),
            ('grpc.http2.min_time_between_pings_ms', 10000),
            ('grpc.http2.min_ping_interval_without_data_ms', 300000)
        ])
        
        stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
        
        auth_request = rustycluster_pb2.AuthenticateRequest(
            username=self.username,
            password=self.password
        )
        
        try:
            auth_response = stub.Authenticate(auth_request)
            
            if auth_response.success:
                self.session_token = auth_response.session_token
                print(f"✓ Authenticated successfully")
                channel.close()
                return True
            else:
                print(f"✗ Authentication failed: {auth_response.message}")
                channel.close()
                return False
        except Exception as e:
            print(f"✗ Authentication error: {e}")
            channel.close()
            return False
    
    def create_connection_pool(self, pool_size=50):
        """Create a pool of gRPC connections for better performance"""
        print(f"Creating connection pool with {pool_size} connections...")
        
        for i in range(pool_size):
            # Optimized channel options for high throughput
            channel = grpc.insecure_channel(f'{self.host}:{self.port}', options=[
                ('grpc.keepalive_time_ms', 10000),
                ('grpc.keepalive_timeout_ms', 5000),
                ('grpc.keepalive_permit_without_calls', True),
                ('grpc.http2.max_pings_without_data', 0),
                ('grpc.http2.min_time_between_pings_ms', 10000),
                ('grpc.http2.min_ping_interval_without_data_ms', 300000),
                ('grpc.max_send_message_length', 4 * 1024 * 1024),
                ('grpc.max_receive_message_length', 4 * 1024 * 1024),
                ('grpc.http2.max_frame_size', 16384),
                ('grpc.http2.bdp_probe', 1),
            ])
            
            stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
            self.channels.append(channel)
            self.stubs.append(stub)
        
        print(f"✓ Created {len(self.stubs)} connections")
    
    def generate_test_data(self, count):
        """Pre-generate test data for better performance"""
        print(f"Generating {count} test data entries...")
        data = []
        for i in range(count):
            # Generate random key-value pairs
            key = f"loadtest_key_{i}_{random.randint(1000, 9999)}"
            value = f"loadtest_value_{i}_{''.join(random.choices(string.ascii_letters + string.digits, k=20))}"
            data.append((key, value))
        return data
    
    def worker_thread(self, worker_id, operations_data, metadata, results_queue, rate_limiter=None):
        """Optimized worker thread with connection reuse"""
        # Use round-robin to select a stub
        stub = self.stubs[worker_id % len(self.stubs)]
        
        successful_ops = 0
        failed_ops = 0
        total_latency = 0
        
        for key, value in operations_data:
            start_time = time.time()
            
            try:
                request = rustycluster_pb2.SetRequest(
                    key=key,
                    value=value,
                    skip_replication=False
                )
                
                response = stub.Set(request, metadata=metadata, timeout=5.0)
                
                if response.success:
                    successful_ops += 1
                else:
                    failed_ops += 1
                
                end_time = time.time()
                total_latency += (end_time - start_time) * 1000  # Convert to ms
                
            except Exception as e:
                failed_ops += 1
                end_time = time.time()
                total_latency += (end_time - start_time) * 1000
            
            # Rate limiting
            if rate_limiter:
                rate_limiter.acquire()
        
        # Calculate average latency for this worker
        avg_latency = total_latency / len(operations_data) if operations_data else 0
        
        results_queue.put({
            'worker_id': worker_id,
            'successful': successful_ops,
            'failed': failed_ops,
            'avg_latency': avg_latency
        })
    
    def load_test(self, num_operations=200000, concurrency=100, target_rps=10000, connection_pool_size=50):
        """Perform optimized load testing with authentication"""
        if not self.authenticate():
            return
        
        # Create connection pool
        self.create_connection_pool(connection_pool_size)
        
        # Pre-generate test data
        test_data = self.generate_test_data(num_operations)
        
        print(f"Starting optimized load test:")
        print(f"  Operations: {num_operations}")
        print(f"  Concurrency: {concurrency}")
        print(f"  Target RPS: {target_rps}")
        print(f"  Connection Pool: {connection_pool_size}")
        
        # Create metadata with session token
        metadata = [('authorization', f'Bearer {self.session_token}')]
        
        # Calculate operations per worker
        operations_per_worker = num_operations // concurrency
        remaining_operations = num_operations % concurrency
        
        # Create rate limiter if needed
        rate_limiter = None
        if target_rps > 0:
            # Simple rate limiting using time delays
            delay_between_requests = 1.0 / (target_rps / concurrency)
        else:
            delay_between_requests = 0
        
        start_time = time.time()
        
        # Start worker threads with optimized thread pool
        with ThreadPoolExecutor(max_workers=concurrency, thread_name_prefix="LoadTest") as executor:
            futures = []
            
            for worker_id in range(concurrency):
                # Calculate data slice for this worker
                start_idx = worker_id * operations_per_worker
                end_idx = start_idx + operations_per_worker
                
                # Add remaining operations to the last few workers
                if worker_id < remaining_operations:
                    end_idx += 1
                    start_idx += worker_id
                else:
                    start_idx += remaining_operations
                    end_idx += remaining_operations
                
                worker_data = test_data[start_idx:end_idx]
                
                # Submit worker task
                future = executor.submit(
                    self.worker_thread,
                    worker_id,
                    worker_data,
                    metadata,
                    self.results_queue,
                    None  # No rate limiter for now - let threads run at full speed
                )
                futures.append(future)
            
            # Wait for all workers to complete
            completed = 0
            for future in as_completed(futures):
                completed += 1
                if completed % 10 == 0:
                    print(f"Completed {completed}/{concurrency} workers...")
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Collect results
        total_successful = 0
        total_failed = 0
        total_latency = 0
        worker_count = 0
        
        while not self.results_queue.empty():
            result = self.results_queue.get()
            total_successful += result['successful']
            total_failed += result['failed']
            total_latency += result['avg_latency']
            worker_count += 1
        
        actual_rps = num_operations / duration
        avg_latency = total_latency / worker_count if worker_count > 0 else 0
        
        print(f"\n{'='*50}")
        print(f"Load test completed:")
        print(f"Duration: {duration:.2f} seconds")
        print(f"Successful operations: {total_successful}")
        print(f"Failed operations: {total_failed}")
        print(f"Actual RPS: {actual_rps:.2f}")
        print(f"Average Latency: {avg_latency:.2f}ms")
        print(f"Success rate: {(total_successful / num_operations) * 100:.2f}%")
        print(f"{'='*50}")
        
        # Performance analysis
        if actual_rps < target_rps * 0.8:
            print(f"\n⚠️  Performance below target ({actual_rps:.0f} < {target_rps})")
            print("Suggestions:")
            print("- Increase connection pool size (--pool-size)")
            print("- Reduce concurrency and increase connection pool")
            print("- Check RustyCluster server performance")
            print("- Consider using multiple client machines")
        else:
            print(f"\n✅ Performance target achieved! ({actual_rps:.0f} RPS)")
    
    def cleanup(self):
        """Clean up resources"""
        for channel in self.channels:
            channel.close()

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Optimized load test for RustyCluster')
    parser.add_argument('--host', default='localhost', help='RustyCluster host')
    parser.add_argument('--port', type=int, default=50051, help='RustyCluster port')
    parser.add_argument('--username', default='testuser', help='Username')
    parser.add_argument('--password', default='testpass', help='Password')
    parser.add_argument('--operations', '-n', type=int, default=200000, help='Number of operations')
    parser.add_argument('--concurrency', '-c', type=int, default=100, help='Concurrent workers')
    parser.add_argument('--rps', type=int, default=10000, help='Target requests per second')
    parser.add_argument('--pool-size', type=int, default=50, help='Connection pool size')
    
    args = parser.parse_args()
    
    tester = OptimizedLoadTester(args.host, args.port, args.username, args.password)
    
    try:
        tester.load_test(args.operations, args.concurrency, args.rps, args.pool_size)
    finally:
        tester.cleanup()

if __name__ == "__main__":
    main()
