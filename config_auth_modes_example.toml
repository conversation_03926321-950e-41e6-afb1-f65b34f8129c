# Example configuration demonstrating new authentication modes and health check features
# Redis connection URL for the primary node
redis_url = "*********************************"

# List of secondary node URLs that this node will replicate to (Rust nodes)
secondary_nodes = ["http://127.0.0.1:50052"]

# Port number on which the gRPC server will listen
port = 50051

# Maximum number of connections in the Redis connection pool
redis_pool_size = 512

# Number of secondary nodes to which data should be replicated
replication_factor = 1

# Number of secondary nodes that must have the data before considering it consistent
read_consistency = 0

# Enable asynchronous replication
async_replication = true

# Maximum number of retry attempts when connecting to secondary nodes
max_retries = 3

# Delay in milliseconds between retry attempts
retry_delay_ms = 100

# Maximum age in seconds for a replication batch
replication_batch_max_age_secs = 30

# Maximum number of connections per secondary node in the gRPC client pool
secondary_pool_size = 256

# Maximum number of operations in a batch
max_batch_size = 10000

# Interval in milliseconds to flush batches
batch_flush_interval_ms = 20

# Server configuration
tcp_keepalive_secs = 5
tcp_nodelay = true
concurrency_limit = 512
max_concurrent_streams = 4096
chunk_size = 5000
num_shards = 64
worker_threads = 16

# Authentication configuration
auth_enabled = true
auth_username = "admin"
auth_password = "npci"
session_duration_secs = 3600

# NEW AUTHENTICATION FEATURES:

# Authentication mode: "connection_only" or "per_request"
# connection_only: authenticate once per connection, no token validation per request (better performance)
# per_request: validate session token on each request (default, more secure)
auth_mode = "connection_only"

# Enable/disable session token expiry
# true: tokens expire after session_duration_secs (default)
# false: tokens are permanent until application restart (useful for connection_only mode)
auth_token_expiry_enabled = false

# Write consistency configuration (for synchronous replication)
peer_redis_nodes = []
write_consistency = "QUORUM"
quorum_value = 2
write_retry_count = 3
peer_redis_pool_size = 64

# Site replication configuration
site_replication_enabled = false
site_primary_node = ""
site_failover_node = ""
site_replication_retry_count = 3
site_replication_timeout_ms = 5000
site_replication_pool_size = 256

# Connection pool mode
use_physical_connections = true

# NEW HEALTH CHECK AND KEEP-ALIVE FEATURES:

# Enable Redis connection health checks to keep connections alive during idle periods
redis_keepalive_enabled = true

# Interval in seconds for Redis connection health checks
redis_keepalive_interval_secs = 30

# Enable secondary nodes health checks to keep connections alive during idle periods
secondary_nodes_keepalive_enabled = true

# Interval in seconds for secondary nodes health checks
secondary_nodes_keepalive_interval_secs = 30

# Enable site replication nodes health checks to keep connections alive during idle periods
site_nodes_keepalive_enabled = true

# Interval in seconds for site replication nodes health checks
site_nodes_keepalive_interval_secs = 30

# Enable peer Redis nodes health checks to keep connections alive during idle periods
peer_redis_keepalive_enabled = true

# Interval in seconds for peer Redis nodes health checks
peer_redis_keepalive_interval_secs = 30
