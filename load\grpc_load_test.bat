@echo off
REM Batch script for load testing Rusty<PERSON><PERSON> using ghz
REM First, download ghz from https://github.com/bojand/ghz/releases
REM and place it in your PATH or in the same directory as this script

REM Configuration
set SERVER_ADDRESS=localhost:50051
set REQUESTS=200000
set CONCURRENCY=100
set TOTAL_REQUESTS=100000
set CONNECTIONS=20
set KEY_PREFIX=test_key_
set VALUE_PREFIX=test_value_

REM Create a temporary directory for test files
set TEST_DIR=.\load_test_temp
if not exist %TEST_DIR% mkdir %TEST_DIR%

REM Create data files for different operations
set SET_DATA_FILE=%TEST_DIR%\set_data.json
set GET_DATA_FILE=%TEST_DIR%\get_data.json
set DELETE_DATA_FILE=%TEST_DIR%\delete_data.json
set SETEX_DATA_FILE=%TEST_DIR%\setex_data.json

REM Create SET data file
echo { > %SET_DATA_FILE%
echo   "key": "{{.RequestNumber}}_%KEY_PREFIX%", >> %SET_DATA_FILE%
echo   "value": "%VALUE_PREFIX%{{.RequestNumber}}", >> %SET_DATA_FILE%
echo   "skip_replication": false >> %SET_DATA_FILE%
echo } >> %SET_DATA_FILE%

REM Create GET data file
echo { > %GET_DATA_FILE%
echo   "key": "{{.RequestNumber}}_%KEY_PREFIX%" >> %GET_DATA_FILE%
echo } >> %GET_DATA_FILE%

REM Create DELETE data file
echo { > %DELETE_DATA_FILE%
echo   "key": "{{.RequestNumber}}_%KEY_PREFIX%", >> %DELETE_DATA_FILE%
echo   "skip_replication": false >> %DELETE_DATA_FILE%
echo } >> %DELETE_DATA_FILE%

REM Create SETEX data file
echo { > %SETEX_DATA_FILE%
echo   "key": "{{.RequestNumber}}_%KEY_PREFIX%", >> %SETEX_DATA_FILE%
echo   "value": "%VALUE_PREFIX%{{.RequestNumber}}", >> %SETEX_DATA_FILE%
echo   "ttl": 3600, >> %SETEX_DATA_FILE%
echo   "skip_replication": false >> %SETEX_DATA_FILE%
echo } >> %SETEX_DATA_FILE%

REM Main test sequence
echo Starting load test for RustyCluster...

REM Check if RustyCluster is running
tasklist /FI "IMAGENAME eq rustycluster.exe" 2>NUL | find /I /N "rustycluster.exe" 
 

REM Test SET operations

copy NUL %TEST_DIR%\SET_results.html
copy NUL %TEST_DIR%\GET_results.html
copy NUL %TEST_DIR%\DELETE_results.html
copy NUL %TEST_DIR%\SETEX_results.html


echo Running SET test...
set OUTPUT_FILE=%TEST_DIR%\SET_results.html
set CMD=C:\Users\<USER>\Desktop\starters\ghz-windows-x86_64\ghz.exe --insecure  --proto=rustycluster.proto --call rustycluster.KeyValueService.Set
set CMD=%CMD% --connections %CONNECTIONS% --concurrency %CONCURRENCY% --total %REQUESTS% --timeout 30s
set CMD=%CMD% --data-file %SET_DATA_FILE% --format html --output %OUTPUT_FILE%
set CMD=%CMD% --metadata-file=meta.json
set CMD=%CMD% %SERVER_ADDRESS%
echo %CMD%

call %CMD%
echo Completed SET test. Results saved to %OUTPUT_FILE%
start "" %OUTPUT_FILE%


REM Test GET operations
echo Running GET test...
set OUTPUT_FILE=%TEST_DIR%\GET_results.html
set CMD=C:\Users\<USER>\Desktop\starters\ghz-windows-x86_64\ghz.exe --insecure  --proto=rustycluster.proto --call rustycluster.KeyValueService.Get
set CMD=%CMD% --connections %CONNECTIONS% --concurrency %CONCURRENCY% --total %REQUESTS% --timeout 30s
set CMD=%CMD% --data-file %GET_DATA_FILE% --format html --output %OUTPUT_FILE%
set CMD=%CMD% --metadata-file=meta.json
set CMD=%CMD% %SERVER_ADDRESS%
echo %CMD%
call %CMD%
echo Completed GET test. Results saved to %OUTPUT_FILE%
start "" %OUTPUT_FILE%
goto :EOF
REM Test DELETE operations
echo Running DELETE test...
set OUTPUT_FILE=%TEST_DIR%\DELETE_results.html
set CMD=C:\Users\<USER>\Desktop\starters\ghz-windows-x86_64\ghz.exe --insecure  --proto=rustycluster.proto --call rustycluster.KeyValueService.Delete
set CMD=%CMD% --connections %CONNECTIONS% --concurrency %CONCURRENCY% --total %REQUESTS% --timeout 30s
set CMD=%CMD% --data-file %DELETE_DATA_FILE% --format html --output %OUTPUT_FILE%
set CMD=%CMD% --metadata-file=meta.json
set CMD=%CMD% %SERVER_ADDRESS%
echo %CMD%
call %CMD%
echo Completed DELETE test. Results saved to %OUTPUT_FILE%
start "" %OUTPUT_FILE%

REM Test SETEX operations
echo Running SETEX test...
set OUTPUT_FILE=%TEST_DIR%\SETEX_results.html
set CMD=C:\Users\<USER>\Desktop\starters\ghz-windows-x86_64\ghz.exe --insecure --proto=rustycluster.proto --call rustycluster.KeyValueService.SetEx
set CMD=%CMD% --connections %CONNECTIONS% --concurrency %CONCURRENCY% --total %REQUESTS% --timeout 30s
set CMD=%CMD% --data-file %SETEX_DATA_FILE% --format html --output %OUTPUT_FILE%
set CMD=%CMD% %SERVER_ADDRESS%
echo %CMD%
call %CMD%
echo Completed SETEX test. Results saved to %OUTPUT_FILE%
start "" %OUTPUT_FILE%
EOF
:cleanup
REM Clean up
REM echo Cleaning up temporary files...
REM rmdir /S /Q %TEST_DIR%

echo Load test completed.
pause
