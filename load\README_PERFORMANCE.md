# High-Performance Load Testing Guide

## Quick Start: Achieve 10K+ RPS

### Step 1: Start RustyCluster with Performance Config
```bash
# From the main rustycluster directory
cargo run --release load/config_performance.toml
```

### Step 2: Run Optimized Load Test
```bash
# Navigate to load directory
cd load

# Try the ultra-high performance async version
python loadtest_async.py --username testuser --password testpass --operations 200000 --concurrency 2000 --rps 10000 --pool-size 200
```

### Step 3: Scale Up if Successful
```bash
# If 10K works, try higher
python loadtest_async.py --username testuser --password testpass --operations 300000 --concurrency 3000 --rps 15000 --pool-size 300

# Push to maximum
python loadtest_async.py --username testuser --password testpass --operations 500000 --concurrency 5000 --rps 20000 --pool-size 500
```

## Available Scripts

### 1. `loadtest_async.py` (Recommended)
- **Best for**: Maximum performance (10K-20K+ RPS)
- **Technology**: Asyncio + gRPC async
- **Advantages**: No GIL limitations, efficient connection pooling

```bash
python loadtest_async.py --help
```

### 2. `loadtest_optimized.py` 
- **Best for**: High performance with threading (5K-10K RPS)
- **Technology**: Optimized threading + connection pooling
- **Advantages**: More stable, easier to debug

```bash
python loadtest_optimized.py --help
```

### 3. `loadtest_with_auth.py` (Original)
- **Best for**: Basic testing (2K-5K RPS)
- **Technology**: Simple threading
- **Advantages**: Simple, educational

## Performance Comparison

| Script | Expected RPS | Technology | Best Use Case |
|--------|--------------|------------|---------------|
| `loadtest_async.py` | 10K-20K+ | Asyncio | Maximum performance |
| `loadtest_optimized.py` | 5K-10K | Threading + pooling | High performance |
| `loadtest_with_auth.py` | 2K-5K | Basic threading | Basic testing |

## Configuration Files

### `config_performance.toml`
- Optimized for maximum throughput
- `replication_factor = 0` (no replication overhead)
- Large connection pools
- Optimized batching and threading

### `config_auth_test.toml`
- Standard configuration with authentication
- Moderate performance settings
- Good for functional testing

### `config_loadtest.toml`
- Authentication disabled
- Good for baseline performance testing

## Troubleshooting Performance Issues

### Issue: Still Getting Low RPS (< 5K)

**Check:**
1. Are you using `loadtest_async.py`?
2. Is concurrency high enough? (try `--concurrency 2000`)
3. Is pool size adequate? (try `--pool-size 200`)
4. Is RustyCluster using the performance config?

**Solutions:**
```bash
# Increase concurrency and pool size
python loadtest_async.py -c 3000 --pool-size 300 --rps 15000

# Check if server is the bottleneck
python loadtest_async.py -c 1000 --pool-size 100 --rps 5000 -n 50000
```

### Issue: Connection Errors

**Solutions:**
```bash
# Reduce concurrency, increase pool size
python loadtest_async.py -c 1000 --pool-size 200

# Increase timeouts
# (modify timeout=10.0 in the script if needed)
```

### Issue: High Latency

**Check:**
- Network latency: `ping 127.0.0.1`
- Redis performance: `redis-cli --latency-history`
- CPU usage: `htop`

## Expected Results

### With Authentication Enabled
- **Target**: 10,000 RPS
- **Achievable**: 10,000-20,000 RPS
- **Latency**: 1-5ms average

### Without Authentication
- **Target**: 15,000+ RPS  
- **Achievable**: 15,000-25,000 RPS
- **Latency**: 0.5-3ms average

## Quick Test Commands

```bash
# Quick 10K test (2 minutes)
python loadtest_async.py -n 100000 -c 2000 --rps 10000 --pool-size 200

# Quick 15K test (1 minute)
python loadtest_async.py -n 75000 -c 3000 --rps 15000 --pool-size 300

# Maximum performance test
python loadtest_async.py -n 200000 -c 5000 --rps 20000 --pool-size 500
```

## System Requirements

### Client Machine
- **CPU**: 4+ cores recommended
- **RAM**: 4GB+ available
- **Network**: Gigabit connection preferred
- **Python**: 3.7+ with grpcio

### Server Machine (RustyCluster)
- **CPU**: 8+ cores for 10K+ RPS
- **RAM**: 8GB+ recommended
- **Redis**: Properly configured and tuned
- **Network**: Low latency to client

## Next Steps

1. **Start with async script**: Use `loadtest_async.py` for best results
2. **Tune gradually**: Start with 10K RPS, then scale up
3. **Monitor resources**: Watch CPU, memory, network on both client and server
4. **Compare with/without auth**: Measure authentication overhead
5. **Scale horizontally**: Use multiple client machines if needed

The async script should easily achieve your 10K RPS target! 🚀
