# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

import rustycluster_pb2 as rustycluster__pb2

GRPC_GENERATED_VERSION = '1.71.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in rustycluster_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class KeyValueServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Authenticate = channel.unary_unary(
                '/rustycluster.KeyValueService/Authenticate',
                request_serializer=rustycluster__pb2.AuthenticateRequest.SerializeToString,
                response_deserializer=rustycluster__pb2.AuthenticateResponse.FromString,
                _registered_method=True)
        self.Ping = channel.unary_unary(
                '/rustycluster.KeyValueService/Ping',
                request_serializer=rustycluster__pb2.PingRequest.SerializeToString,
                response_deserializer=rustycluster__pb2.PingResponse.FromString,
                _registered_method=True)
        self.Set = channel.unary_unary(
                '/rustycluster.KeyValueService/Set',
                request_serializer=rustycluster__pb2.SetRequest.SerializeToString,
                response_deserializer=rustycluster__pb2.SetResponse.FromString,
                _registered_method=True)
        self.Get = channel.unary_unary(
                '/rustycluster.KeyValueService/Get',
                request_serializer=rustycluster__pb2.GetRequest.SerializeToString,
                response_deserializer=rustycluster__pb2.GetResponse.FromString,
                _registered_method=True)
        self.Delete = channel.unary_unary(
                '/rustycluster.KeyValueService/Delete',
                request_serializer=rustycluster__pb2.DeleteRequest.SerializeToString,
                response_deserializer=rustycluster__pb2.DeleteResponse.FromString,
                _registered_method=True)
        self.SetEx = channel.unary_unary(
                '/rustycluster.KeyValueService/SetEx',
                request_serializer=rustycluster__pb2.SetExRequest.SerializeToString,
                response_deserializer=rustycluster__pb2.SetExResponse.FromString,
                _registered_method=True)
        self.SetExpiry = channel.unary_unary(
                '/rustycluster.KeyValueService/SetExpiry',
                request_serializer=rustycluster__pb2.SetExpiryRequest.SerializeToString,
                response_deserializer=rustycluster__pb2.SetExpiryResponse.FromString,
                _registered_method=True)
        self.IncrBy = channel.unary_unary(
                '/rustycluster.KeyValueService/IncrBy',
                request_serializer=rustycluster__pb2.IncrByRequest.SerializeToString,
                response_deserializer=rustycluster__pb2.IncrByResponse.FromString,
                _registered_method=True)
        self.DecrBy = channel.unary_unary(
                '/rustycluster.KeyValueService/DecrBy',
                request_serializer=rustycluster__pb2.DecrByRequest.SerializeToString,
                response_deserializer=rustycluster__pb2.DecrByResponse.FromString,
                _registered_method=True)
        self.IncrByFloat = channel.unary_unary(
                '/rustycluster.KeyValueService/IncrByFloat',
                request_serializer=rustycluster__pb2.IncrByFloatRequest.SerializeToString,
                response_deserializer=rustycluster__pb2.IncrByFloatResponse.FromString,
                _registered_method=True)
        self.HSet = channel.unary_unary(
                '/rustycluster.KeyValueService/HSet',
                request_serializer=rustycluster__pb2.HSetRequest.SerializeToString,
                response_deserializer=rustycluster__pb2.HSetResponse.FromString,
                _registered_method=True)
        self.HGet = channel.unary_unary(
                '/rustycluster.KeyValueService/HGet',
                request_serializer=rustycluster__pb2.HGetRequest.SerializeToString,
                response_deserializer=rustycluster__pb2.HGetResponse.FromString,
                _registered_method=True)
        self.HGetAll = channel.unary_unary(
                '/rustycluster.KeyValueService/HGetAll',
                request_serializer=rustycluster__pb2.HGetAllRequest.SerializeToString,
                response_deserializer=rustycluster__pb2.HGetAllResponse.FromString,
                _registered_method=True)
        self.HIncrBy = channel.unary_unary(
                '/rustycluster.KeyValueService/HIncrBy',
                request_serializer=rustycluster__pb2.HIncrByRequest.SerializeToString,
                response_deserializer=rustycluster__pb2.HIncrByResponse.FromString,
                _registered_method=True)
        self.HDecrBy = channel.unary_unary(
                '/rustycluster.KeyValueService/HDecrBy',
                request_serializer=rustycluster__pb2.HDecrByRequest.SerializeToString,
                response_deserializer=rustycluster__pb2.HDecrByResponse.FromString,
                _registered_method=True)
        self.HIncrByFloat = channel.unary_unary(
                '/rustycluster.KeyValueService/HIncrByFloat',
                request_serializer=rustycluster__pb2.HIncrByFloatRequest.SerializeToString,
                response_deserializer=rustycluster__pb2.HIncrByFloatResponse.FromString,
                _registered_method=True)
        self.BatchWrite = channel.unary_unary(
                '/rustycluster.KeyValueService/BatchWrite',
                request_serializer=rustycluster__pb2.BatchWriteRequest.SerializeToString,
                response_deserializer=rustycluster__pb2.BatchWriteResponse.FromString,
                _registered_method=True)


class KeyValueServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Authenticate(self, request, context):
        """Authentication operations
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Ping(self, request, context):
        """System operations
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Set(self, request, context):
        """String operations
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Get(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Delete(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetEx(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetExpiry(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def IncrBy(self, request, context):
        """Numeric operations
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DecrBy(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def IncrByFloat(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def HSet(self, request, context):
        """Hash operations
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def HGet(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def HGetAll(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def HIncrBy(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def HDecrBy(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def HIncrByFloat(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BatchWrite(self, request, context):
        """Batch operations
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_KeyValueServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Authenticate': grpc.unary_unary_rpc_method_handler(
                    servicer.Authenticate,
                    request_deserializer=rustycluster__pb2.AuthenticateRequest.FromString,
                    response_serializer=rustycluster__pb2.AuthenticateResponse.SerializeToString,
            ),
            'Ping': grpc.unary_unary_rpc_method_handler(
                    servicer.Ping,
                    request_deserializer=rustycluster__pb2.PingRequest.FromString,
                    response_serializer=rustycluster__pb2.PingResponse.SerializeToString,
            ),
            'Set': grpc.unary_unary_rpc_method_handler(
                    servicer.Set,
                    request_deserializer=rustycluster__pb2.SetRequest.FromString,
                    response_serializer=rustycluster__pb2.SetResponse.SerializeToString,
            ),
            'Get': grpc.unary_unary_rpc_method_handler(
                    servicer.Get,
                    request_deserializer=rustycluster__pb2.GetRequest.FromString,
                    response_serializer=rustycluster__pb2.GetResponse.SerializeToString,
            ),
            'Delete': grpc.unary_unary_rpc_method_handler(
                    servicer.Delete,
                    request_deserializer=rustycluster__pb2.DeleteRequest.FromString,
                    response_serializer=rustycluster__pb2.DeleteResponse.SerializeToString,
            ),
            'SetEx': grpc.unary_unary_rpc_method_handler(
                    servicer.SetEx,
                    request_deserializer=rustycluster__pb2.SetExRequest.FromString,
                    response_serializer=rustycluster__pb2.SetExResponse.SerializeToString,
            ),
            'SetExpiry': grpc.unary_unary_rpc_method_handler(
                    servicer.SetExpiry,
                    request_deserializer=rustycluster__pb2.SetExpiryRequest.FromString,
                    response_serializer=rustycluster__pb2.SetExpiryResponse.SerializeToString,
            ),
            'IncrBy': grpc.unary_unary_rpc_method_handler(
                    servicer.IncrBy,
                    request_deserializer=rustycluster__pb2.IncrByRequest.FromString,
                    response_serializer=rustycluster__pb2.IncrByResponse.SerializeToString,
            ),
            'DecrBy': grpc.unary_unary_rpc_method_handler(
                    servicer.DecrBy,
                    request_deserializer=rustycluster__pb2.DecrByRequest.FromString,
                    response_serializer=rustycluster__pb2.DecrByResponse.SerializeToString,
            ),
            'IncrByFloat': grpc.unary_unary_rpc_method_handler(
                    servicer.IncrByFloat,
                    request_deserializer=rustycluster__pb2.IncrByFloatRequest.FromString,
                    response_serializer=rustycluster__pb2.IncrByFloatResponse.SerializeToString,
            ),
            'HSet': grpc.unary_unary_rpc_method_handler(
                    servicer.HSet,
                    request_deserializer=rustycluster__pb2.HSetRequest.FromString,
                    response_serializer=rustycluster__pb2.HSetResponse.SerializeToString,
            ),
            'HGet': grpc.unary_unary_rpc_method_handler(
                    servicer.HGet,
                    request_deserializer=rustycluster__pb2.HGetRequest.FromString,
                    response_serializer=rustycluster__pb2.HGetResponse.SerializeToString,
            ),
            'HGetAll': grpc.unary_unary_rpc_method_handler(
                    servicer.HGetAll,
                    request_deserializer=rustycluster__pb2.HGetAllRequest.FromString,
                    response_serializer=rustycluster__pb2.HGetAllResponse.SerializeToString,
            ),
            'HIncrBy': grpc.unary_unary_rpc_method_handler(
                    servicer.HIncrBy,
                    request_deserializer=rustycluster__pb2.HIncrByRequest.FromString,
                    response_serializer=rustycluster__pb2.HIncrByResponse.SerializeToString,
            ),
            'HDecrBy': grpc.unary_unary_rpc_method_handler(
                    servicer.HDecrBy,
                    request_deserializer=rustycluster__pb2.HDecrByRequest.FromString,
                    response_serializer=rustycluster__pb2.HDecrByResponse.SerializeToString,
            ),
            'HIncrByFloat': grpc.unary_unary_rpc_method_handler(
                    servicer.HIncrByFloat,
                    request_deserializer=rustycluster__pb2.HIncrByFloatRequest.FromString,
                    response_serializer=rustycluster__pb2.HIncrByFloatResponse.SerializeToString,
            ),
            'BatchWrite': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchWrite,
                    request_deserializer=rustycluster__pb2.BatchWriteRequest.FromString,
                    response_serializer=rustycluster__pb2.BatchWriteResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'rustycluster.KeyValueService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('rustycluster.KeyValueService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class KeyValueService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Authenticate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/rustycluster.KeyValueService/Authenticate',
            rustycluster__pb2.AuthenticateRequest.SerializeToString,
            rustycluster__pb2.AuthenticateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Ping(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/rustycluster.KeyValueService/Ping',
            rustycluster__pb2.PingRequest.SerializeToString,
            rustycluster__pb2.PingResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Set(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/rustycluster.KeyValueService/Set',
            rustycluster__pb2.SetRequest.SerializeToString,
            rustycluster__pb2.SetResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Get(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/rustycluster.KeyValueService/Get',
            rustycluster__pb2.GetRequest.SerializeToString,
            rustycluster__pb2.GetResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Delete(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/rustycluster.KeyValueService/Delete',
            rustycluster__pb2.DeleteRequest.SerializeToString,
            rustycluster__pb2.DeleteResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetEx(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/rustycluster.KeyValueService/SetEx',
            rustycluster__pb2.SetExRequest.SerializeToString,
            rustycluster__pb2.SetExResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SetExpiry(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/rustycluster.KeyValueService/SetExpiry',
            rustycluster__pb2.SetExpiryRequest.SerializeToString,
            rustycluster__pb2.SetExpiryResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def IncrBy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/rustycluster.KeyValueService/IncrBy',
            rustycluster__pb2.IncrByRequest.SerializeToString,
            rustycluster__pb2.IncrByResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DecrBy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/rustycluster.KeyValueService/DecrBy',
            rustycluster__pb2.DecrByRequest.SerializeToString,
            rustycluster__pb2.DecrByResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def IncrByFloat(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/rustycluster.KeyValueService/IncrByFloat',
            rustycluster__pb2.IncrByFloatRequest.SerializeToString,
            rustycluster__pb2.IncrByFloatResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def HSet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/rustycluster.KeyValueService/HSet',
            rustycluster__pb2.HSetRequest.SerializeToString,
            rustycluster__pb2.HSetResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def HGet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/rustycluster.KeyValueService/HGet',
            rustycluster__pb2.HGetRequest.SerializeToString,
            rustycluster__pb2.HGetResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def HGetAll(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/rustycluster.KeyValueService/HGetAll',
            rustycluster__pb2.HGetAllRequest.SerializeToString,
            rustycluster__pb2.HGetAllResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def HIncrBy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/rustycluster.KeyValueService/HIncrBy',
            rustycluster__pb2.HIncrByRequest.SerializeToString,
            rustycluster__pb2.HIncrByResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def HDecrBy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/rustycluster.KeyValueService/HDecrBy',
            rustycluster__pb2.HDecrByRequest.SerializeToString,
            rustycluster__pb2.HDecrByResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def HIncrByFloat(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/rustycluster.KeyValueService/HIncrByFloat',
            rustycluster__pb2.HIncrByFloatRequest.SerializeToString,
            rustycluster__pb2.HIncrByFloatResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BatchWrite(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/rustycluster.KeyValueService/BatchWrite',
            rustycluster__pb2.BatchWriteRequest.SerializeToString,
            rustycluster__pb2.BatchWriteResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
