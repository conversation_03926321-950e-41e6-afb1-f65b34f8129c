use log4rs::append::rolling_file::{
    RollingFileAppender,
    policy::compound::{
        CompoundPolicy,
        roll::fixed_window::FixedWindowRoller,
        trigger::size::SizeTrigger,
    },
};
use log4rs::config::{Appender, Config as LogConfig, Root};
use log4rs::encode::pattern::PatternEncoder;
use log::LevelFilter;
use std::path::PathBuf;
use serde::Deserialize;
use config::Config as AppConfig;

#[derive(Deserialize, Debug)]
pub struct LoggingParams {
    #[serde(default = "default_log_file")]
    pub file: String,
    #[serde(default = "default_max_size")]
    pub max_size: u64,
    #[serde(default = "default_max_files")]
    pub max_files: u32,
    #[serde(default = "default_log_level")]
    pub level: String,
    #[serde(default = "default_pattern")]
    pub pattern: String,
}

fn default_log_file() -> String {
    "rustycluster.log".to_string()
}

fn default_max_size() -> u64 {
    10 * 1024 * 1024 // 10MB
}

fn default_max_files() -> u32 {
    5
}

fn default_log_level() -> String {
    "info".to_string()
}

fn default_pattern() -> String {
    "{d(%Y-%m-%d %H:%M:%S%.3f)} - {l} - {M} - {m}\n".to_string()
}

impl Default for LoggingParams {
    fn default() -> Self {
        Self {
            file: default_log_file(),
            max_size: default_max_size(),
            max_files: default_max_files(),
            level: default_log_level(),
            pattern: default_pattern(),
        }
    }
}

impl LoggingParams {
    pub fn from_file(file_path: Option<&str>) -> Result<Self, Box<dyn std::error::Error>> {
        let config_file = file_path.unwrap_or("logconfig.toml");
        let settings = AppConfig::builder()
            .add_source(config::File::with_name(config_file).required(false))
            .build()?;
        
        let params: LoggingParams = settings.try_deserialize()?;
        Ok(params)
    }
}

pub fn setup_logging(params: &LoggingParams) -> Result<(), Box<dyn std::error::Error>> {
    let log_config = LogConfig::builder()
        .appender(Appender::builder()
            .build("file", Box::new(RollingFileAppender::builder()
                .encoder(Box::new(PatternEncoder::new(&params.pattern)))
                .build(
                    PathBuf::from(&params.file),
                    Box::new(CompoundPolicy::new(
                        Box::new(SizeTrigger::new(params.max_size)),
                        Box::new(FixedWindowRoller::builder()
                            .build("rustycluster.{}.log", params.max_files)
                            .unwrap()),
                    )),
                )
                .unwrap())))
        .build(Root::builder()
            .appender("file")
            .build(params.level.parse::<LevelFilter>().unwrap_or(LevelFilter::Info)))
        .unwrap();

    log4rs::init_config(log_config)?;
    Ok(())
} 