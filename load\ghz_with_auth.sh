#!/bin/bash

# Load testing script for Rusty<PERSON>luster with authentication using ghz
# This script gets a session token and then runs ghz with authentication

# Configuration
HOST="127.0.0.1"
PORT="50051"
USERNAME="testuser"
PASSWORD="testpass"
PROTO_FILE="../rustycluster.proto"
DATA_FILE="../set-data.json"
RPS=10000
REQUESTS=200000
CONNECTIONS=1000

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}RustyCluster Load Testing with Authentication${NC}"
echo "=============================================="

# Function to get session token using grpcurl
get_session_token() {
    echo -e "${YELLOW}Getting session token...${NC}"
    
    # Check if grpcurl is available
    if ! command -v grpcurl &> /dev/null; then
        echo -e "${RED}Error: grpcurl is required but not installed.${NC}"
        echo "Install it with: go install github.com/fullstorydev/grpcurl/cmd/grpcurl@latest"
        exit 1
    fi
    
    # Authenticate and get token
    TOKEN_RESPONSE=$(grpcurl -plaintext -d "{\"username\":\"$USERNAME\",\"password\":\"$PASSWORD\"}" \
        -import-path . -proto $PROTO_FILE \
        $HOST:$PORT rustycluster.KeyValueService.Authenticate 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        # Extract session token from JSON response
        SESSION_TOKEN=$(echo "$TOKEN_RESPONSE" | grep -o '"sessionToken":"[^"]*"' | cut -d'"' -f4)
        
        if [ -n "$SESSION_TOKEN" ]; then
            echo -e "${GREEN}✓ Authentication successful${NC}"
            echo "Session token: ${SESSION_TOKEN:0:20}..."
            return 0
        else
            echo -e "${RED}✗ Failed to extract session token${NC}"
            echo "Response: $TOKEN_RESPONSE"
            return 1
        fi
    else
        echo -e "${RED}✗ Authentication failed${NC}"
        return 1
    fi
}

# Function to run ghz with authentication
run_load_test() {
    echo -e "${YELLOW}Starting load test...${NC}"
    echo "RPS: $RPS, Requests: $REQUESTS, Connections: $CONNECTIONS"
    
    # Check if ghz is available
    if ! command -v ghz.exe &> /dev/null && ! command -v ghz &> /dev/null; then
        echo -e "${RED}Error: ghz is required but not installed.${NC}"
        echo "Download it from: https://github.com/bojand/ghz/releases"
        exit 1
    fi
    
    # Determine ghz command
    GHZ_CMD="ghz"
    if command -v ghz.exe &> /dev/null; then
        GHZ_CMD="ghz.exe"
    fi
    
    # Run ghz with authentication header
    $GHZ_CMD --insecure \
        --proto=$PROTO_FILE \
        --call=rustycluster.KeyValueService.Set \
        --data-file=$DATA_FILE \
        --metadata="authorization:Bearer $SESSION_TOKEN" \
        --rps $RPS \
        -n $REQUESTS \
        -c $CONNECTIONS \
        $HOST:$PORT
}

# Function to run load test without authentication
run_load_test_no_auth() {
    echo -e "${YELLOW}Starting load test without authentication...${NC}"
    echo "RPS: $RPS, Requests: $REQUESTS, Connections: $CONNECTIONS"
    
    # Determine ghz command
    GHZ_CMD="ghz"
    if command -v ghz.exe &> /dev/null; then
        GHZ_CMD="ghz.exe"
    fi
    
    # Run ghz without authentication
    $GHZ_CMD --insecure \
        --proto=$PROTO_FILE \
        --call=rustycluster.KeyValueService.Set \
        --data-file=$DATA_FILE \
        --rps $RPS \
        -n $REQUESTS \
        -c $CONNECTIONS \
        $HOST:$PORT
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --no-auth)
            NO_AUTH=true
            shift
            ;;
        --host)
            HOST="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --username)
            USERNAME="$2"
            shift 2
            ;;
        --password)
            PASSWORD="$2"
            shift 2
            ;;
        --rps)
            RPS="$2"
            shift 2
            ;;
        --requests|-n)
            REQUESTS="$2"
            shift 2
            ;;
        --connections|-c)
            CONNECTIONS="$2"
            shift 2
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --no-auth           Run without authentication"
            echo "  --host HOST         RustyCluster host (default: 127.0.0.1)"
            echo "  --port PORT         RustyCluster port (default: 50051)"
            echo "  --username USER     Username for authentication (default: testuser)"
            echo "  --password PASS     Password for authentication (default: testpass)"
            echo "  --rps RPS           Requests per second (default: 10000)"
            echo "  --requests N        Total number of requests (default: 200000)"
            echo "  --connections C     Number of connections (default: 1000)"
            echo "  --help              Show this help message"
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            exit 1
            ;;
    esac
done

# Main execution
if [ "$NO_AUTH" = true ]; then
    echo -e "${YELLOW}Running load test without authentication${NC}"
    run_load_test_no_auth
else
    # Get session token and run load test
    if get_session_token; then
        run_load_test
    else
        echo -e "${RED}Failed to get session token. Exiting.${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}Load test completed!${NC}"
