#!/usr/bin/env python3
"""
Ultra-high performance load testing script optimized for replication testing.
Designed to achieve 10K+ RPS with replication_factor = 2.

This version includes:
- Optimized connection pooling for replication overhead
- Adaptive concurrency management
- Replication-aware performance monitoring
- Progressive load ramping
"""

import grpc
import asyncio
import time
import sys
import random
import string
import statistics
from typing import List, Tuple

try:
    import rustycluster_pb2
    import rustycluster_pb2_grpc
except ImportError:
    print("Error: gRPC Python stubs not found.")
    sys.exit(1)

class ReplicationLoadTester:
    def __init__(self, host="localhost", port=50051, username="", password=""):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.session_token = None
        self.channels = []
        self.stubs = []
        
    async def authenticate(self):
        """Authenticate if credentials provided"""
        if not self.username or not self.password:
            print("✓ No authentication required")
            return True
            
        channel = grpc.aio.insecure_channel(f'{self.host}:{self.port}')
        stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
        
        auth_request = rustycluster_pb2.AuthenticateRequest(
            username=self.username,
            password=self.password
        )
        
        try:
            auth_response = await stub.Authenticate(auth_request)
            
            if auth_response.success:
                self.session_token = auth_response.session_token
                print(f"✓ Authenticated successfully")
                await channel.close()
                return True
            else:
                print(f"✗ Authentication failed: {auth_response.message}")
                await channel.close()
                return False
        except Exception as e:
            print(f"✗ Authentication error: {e}")
            await channel.close()
            return False
    
    async def create_optimized_connection_pool(self, pool_size=200):
        """Create connection pool optimized for replication workloads"""
        print(f"Creating replication-optimized connection pool with {pool_size} connections...")
        
        # Optimized gRPC options for replication workloads
        channel_options = [
            ('grpc.keepalive_time_ms', 5000),
            ('grpc.keepalive_timeout_ms', 2000),
            ('grpc.keepalive_permit_without_calls', True),
            ('grpc.http2.max_pings_without_data', 0),
            ('grpc.http2.min_time_between_pings_ms', 5000),
            ('grpc.http2.min_ping_interval_without_data_ms', 300000),
            ('grpc.max_send_message_length', 8 * 1024 * 1024),
            ('grpc.max_receive_message_length', 8 * 1024 * 1024),
            ('grpc.http2.max_frame_size', 32768),
            ('grpc.http2.bdp_probe', 1),
            ('grpc.http2.write_buffer_size', 1024 * 1024),
            ('grpc.http2.read_buffer_size', 1024 * 1024),
        ]
        
        for i in range(pool_size):
            channel = grpc.aio.insecure_channel(f'{self.host}:{self.port}', options=channel_options)
            stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
            self.channels.append(channel)
            self.stubs.append(stub)
        
        print(f"✓ Created {len(self.stubs)} optimized connections")
    
    def generate_replication_test_data(self, count):
        """Generate test data optimized for replication testing"""
        print(f"Generating {count} replication test data entries...")
        data = []
        for i in range(count):
            # Use consistent key patterns for better replication batching
            key = f"repl_test_{i:08d}_{random.randint(1000, 9999)}"
            # Larger values to test replication bandwidth
            value = f"replication_data_{i}_{''.join(random.choices(string.ascii_letters + string.digits, k=100))}"
            data.append((key, value))
        return data
    
    async def single_replication_operation(self, stub, key, value, metadata, semaphore):
        """Perform a single Set operation optimized for replication"""
        async with semaphore:
            start_time = time.time()
            try:
                request = rustycluster_pb2.SetRequest(
                    key=key,
                    value=value,
                    skip_replication=False  # Ensure replication happens
                )
                
                response = await stub.Set(request, metadata=metadata, timeout=15.0)
                end_time = time.time()
                
                return {
                    'success': response.success,
                    'latency': (end_time - start_time) * 1000,
                    'error': None
                }
            except Exception as e:
                end_time = time.time()
                return {
                    'success': False,
                    'latency': (end_time - start_time) * 1000,
                    'error': str(e)
                }
    
    async def progressive_load_test(self, target_operations=200000, target_rps=10000, connection_pool_size=200):
        """Progressive load testing optimized for replication"""
        if not await self.authenticate():
            return
        
        await self.create_optimized_connection_pool(connection_pool_size)
        
        # Create metadata
        metadata = []
        if self.session_token:
            metadata = [('authorization', f'Bearer {self.session_token}')]
        
        print(f"Starting progressive replication load test:")
        print(f"  Target Operations: {target_operations}")
        print(f"  Target RPS: {target_rps}")
        print(f"  Connection Pool: {connection_pool_size}")
        print(f"  Replication Factor: 2 (each write = 3 total operations)")
        
        # Progressive load phases
        phases = [
            {"rps": 2000, "duration": 30, "name": "Warmup"},
            {"rps": 5000, "duration": 30, "name": "Ramp-up"},
            {"rps": 8000, "duration": 30, "name": "Pre-target"},
            {"rps": target_rps, "duration": 60, "name": "Target Load"},
            {"rps": target_rps * 1.2, "duration": 30, "name": "Peak Test"},
        ]
        
        all_results = []
        
        for phase in phases:
            print(f"\n🚀 Phase: {phase['name']} - {phase['rps']} RPS for {phase['duration']}s")
            
            # Calculate operations for this phase
            phase_operations = int(phase['rps'] * phase['duration'])
            test_data = self.generate_replication_test_data(phase_operations)
            
            # Calculate optimal concurrency for replication workload
            # Higher concurrency needed due to replication latency
            concurrency = min(phase['rps'] // 2, connection_pool_size * 2)
            semaphore = asyncio.Semaphore(concurrency)
            
            print(f"  Operations: {phase_operations}, Concurrency: {concurrency}")
            
            start_time = time.time()
            results = []
            
            # Create all tasks for this phase
            tasks = []
            for key, value in test_data:
                stub = self.stubs[len(tasks) % len(self.stubs)]
                task = self.single_replication_operation(stub, key, value, metadata, semaphore)
                tasks.append(task)
            
            # Execute with progress monitoring
            batch_size = 1000
            for i in range(0, len(tasks), batch_size):
                batch = tasks[i:i + batch_size]
                batch_results = await asyncio.gather(*batch, return_exceptions=True)
                results.extend(batch_results)
                
                # Progress update
                if (i + batch_size) % 5000 == 0:
                    elapsed = time.time() - start_time
                    current_rps = len(results) / elapsed if elapsed > 0 else 0
                    print(f"    Progress: {len(results)}/{len(tasks)} ops, Current RPS: {current_rps:.0f}")
            
            end_time = time.time()
            phase_duration = end_time - start_time
            
            # Analyze phase results
            successful_ops = sum(1 for r in results if isinstance(r, dict) and r.get('success', False))
            failed_ops = len(results) - successful_ops
            latencies = [r['latency'] for r in results if isinstance(r, dict) and 'latency' in r]
            
            actual_rps = len(results) / phase_duration
            avg_latency = statistics.mean(latencies) if latencies else 0
            p95_latency = statistics.quantiles(latencies, n=20)[18] if len(latencies) > 20 else 0
            
            print(f"  ✓ Phase Results:")
            print(f"    Duration: {phase_duration:.2f}s")
            print(f"    Actual RPS: {actual_rps:.2f} (target: {phase['rps']})")
            print(f"    Success Rate: {(successful_ops / len(results)) * 100:.1f}%")
            print(f"    Avg Latency: {avg_latency:.2f}ms")
            print(f"    P95 Latency: {p95_latency:.2f}ms")
            
            all_results.extend(results)
            
            # Brief pause between phases
            if phase != phases[-1]:
                print("  Cooling down for 5 seconds...")
                await asyncio.sleep(5)
        
        # Final summary
        total_successful = sum(1 for r in all_results if isinstance(r, dict) and r.get('success', False))
        total_failed = len(all_results) - total_successful
        all_latencies = [r['latency'] for r in all_results if isinstance(r, dict) and 'latency' in r]
        
        print(f"\n{'='*70}")
        print(f"REPLICATION LOAD TEST SUMMARY")
        print(f"{'='*70}")
        print(f"Total Operations: {len(all_results)}")
        print(f"Successful: {total_successful}")
        print(f"Failed: {total_failed}")
        print(f"Success Rate: {(total_successful / len(all_results)) * 100:.2f}%")
        print(f"")
        print(f"Latency Analysis:")
        if all_latencies:
            print(f"  Average: {statistics.mean(all_latencies):.2f}ms")
            print(f"  Median: {statistics.median(all_latencies):.2f}ms")
            print(f"  P95: {statistics.quantiles(all_latencies, n=20)[18]:.2f}ms")
            print(f"  P99: {statistics.quantiles(all_latencies, n=100)[98]:.2f}ms")
        print(f"")
        print(f"Replication Performance:")
        print(f"  Each client operation triggers 3 total operations (1 primary + 2 replicas)")
        print(f"  Effective system throughput: {total_successful * 3} total operations")
        print(f"{'='*70}")
        
        # Performance assessment
        target_phase = next(p for p in phases if p['name'] == 'Target Load')
        target_ops = target_phase['rps'] * target_phase['duration']
        target_success = sum(1 for r in all_results[-target_ops:] if isinstance(r, dict) and r.get('success', False))
        target_success_rate = (target_success / target_ops) * 100 if target_ops > 0 else 0
        
        if target_success_rate >= 95:
            print(f"\n🎉 SUCCESS: Achieved {target_rps} RPS with replication_factor=2!")
        elif target_success_rate >= 80:
            print(f"\n📊 CLOSE: {target_success_rate:.1f}% success rate at target load")
        else:
            print(f"\n⚠️  NEEDS OPTIMIZATION: {target_success_rate:.1f}% success rate at target load")
    
    async def cleanup(self):
        """Clean up resources"""
        for channel in self.channels:
            await channel.close()

async def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Replication-optimized load test')
    parser.add_argument('--host', default='localhost', help='RustyCluster host')
    parser.add_argument('--port', type=int, default=50051, help='RustyCluster port')
    parser.add_argument('--username', default='', help='Username (optional)')
    parser.add_argument('--password', default='', help='Password (optional)')
    parser.add_argument('--operations', '-n', type=int, default=200000, help='Target operations')
    parser.add_argument('--rps', type=int, default=10000, help='Target RPS')
    parser.add_argument('--pool-size', type=int, default=200, help='Connection pool size')
    
    args = parser.parse_args()
    
    tester = ReplicationLoadTester(args.host, args.port, args.username, args.password)
    
    try:
        await tester.progressive_load_test(args.operations, args.rps, args.pool_size)
    finally:
        await tester.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
