# High-performance configuration for RustyCluster load testing
# Optimized for maximum throughput with authentication enabled

# Redis connection URL for the primary node
redis_url = "redis://settlenxt:npci@127.0.0.1:6379"

# List of secondary node URLs that this node will replicate to
secondary_nodes = ["http://127.0.0.1:50052", "http://127.0.0.1:50053"]

# Port number on which the gRPC server will listen
port = 50051

# Maximize connection pools for high throughput
redis_pool_size = 2048
secondary_pool_size = 1024

# Disable replication for pure performance testing
# Set to 0 for maximum performance, increase for replication testing
replication_factor = 0
read_consistency = 0
async_replication = true

# Optimize retry settings for performance
max_retries = 3
retry_delay_ms = 10

# Optimize batching for high throughput
replication_batch_max_age_secs = 30
max_batch_size = 100000
batch_flush_interval_ms = 10

# Optimize network settings for high performance
tcp_keepalive_secs = 10
tcp_nodelay = true
concurrency_limit = 2048
max_concurrent_streams = 8192

# Optimize batch processing
chunk_size = 100000
num_shards = 256

# Maximize worker threads for high concurrency
worker_threads = 32

# Authentication configuration
auth_enabled = true
auth_username = "testuser"
auth_password = "testpass"
session_duration_secs = 7200  # 2 hours - longer sessions for load testing
