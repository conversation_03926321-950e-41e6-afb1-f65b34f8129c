#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to get a session token from RustyCluster for use in load testing.
This token can then be used with ghz for authenticated load testing.

Requirements:
- pip install grpcio grpcio-tools
- Generate Python gRPC stubs from rustycluster.proto
"""

import grpc
import sys

try:
    import rustycluster_pb2
    import rustycluster_pb2_grpc
except ImportError:
    print("Error: gRPC Python stubs not found.")
    print("Please generate them first with:")
    print("python -m grpc_tools.protoc -I./proto --python_out=. --grpc_python_out=. proto/rustycluster.proto")
    sys.exit(1)

def get_session_token(host="localhost", port=50051, username="testuser", password="testpass"):
    """Get a session token for load testing"""
    
    channel = grpc.insecure_channel(f'{host}:{port}')
    stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
    
    try:
        # Authenticate
        auth_request = rustycluster_pb2.AuthenticateRequest(
            username=username,
            password=password
        )
        
        auth_response = stub.Authenticate(auth_request)
        
        if auth_response.success:
            print(f"Session token: {auth_response.session_token}")
            return auth_response.session_token
        else:
            print(f"Authentication failed: {auth_response.message}")
            return None
            
    except grpc.RpcError as e:
        print(f"gRPC error: {e}")
        return None
    finally:
        channel.close()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Get RustyCluster session token')
    parser.add_argument('--host', default='localhost', help='RustyCluster host')
    parser.add_argument('--port', type=int, default=50051, help='RustyCluster port')
    parser.add_argument('--username', default='testuser', help='Username')
    parser.add_argument('--password', default='testpass', help='Password')
    
    args = parser.parse_args()
    
    token = get_session_token(args.host, args.port, args.username, args.password)
    if token:
        print(f"\nUse this token with ghz:")
        print(f'--metadata="authorization:Bearer {token}"')
    else:
        sys.exit(1)
