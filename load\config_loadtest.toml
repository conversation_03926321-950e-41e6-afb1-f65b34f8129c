# Load testing configuration - Authentication disabled for performance testing
redis_url = "redis://settlenxt:npci@127.0.0.1:6379"

# List of secondary node URLs that this node will replicate to
secondary_nodes = ["http://127.0.0.1:50052", "http://127.0.0.1:50053"]

# Port number on which the gRPC server will listen
port = 50051

# Maximum number of connections in the Redis connection pool
redis_pool_size = 1024

# Number of secondary nodes to which data should be replicated
# Set to 0 for maximum performance during load testing
replication_factor = 0

# Number of secondary nodes that must have the data before considering it consistent
read_consistency = 0

# If true, replication to secondary nodes happens asynchronously
async_replication = true

# Maximum number of retry attempts when connecting to secondary nodes
max_retries = 3

# Delay in milliseconds between retry attempts
retry_delay_ms = 10

# Maximum age in seconds for a replication batch
replication_batch_max_age_secs = 30

# Maximum number of connections per secondary node in the gRPC client pool
secondary_pool_size = 512

# Maximum number of operations in a batch
max_batch_size = 1000

# Interval in milliseconds to flush batches
batch_flush_interval_ms = 50

# TCP keepalive timeout in seconds
tcp_keepalive_secs = 30

# Enable TCP_NODELAY for better latency
tcp_nodelay = true

# Limits the number of concurrent requests per connection
concurrency_limit = 1024

# Controls maximum number of concurrent HTTP/2 streams per connection
max_concurrent_streams = 8192

# Replication chunk size for batch processing
chunk_size = 10000

# Number of shards for batch collectors
num_shards = 128

# Configure worker threads based on the CPU size
worker_threads = 16

# Authentication configuration - DISABLED for load testing
auth_enabled = false
auth_username = ""
auth_password = ""
session_duration_secs = 3600
