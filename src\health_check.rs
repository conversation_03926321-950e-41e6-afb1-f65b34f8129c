use std::sync::Arc;
use std::time::Duration;
use tokio::time::interval;
use tracing::{debug, info, warn};
use deadpool_redis::Pool;
use crate::grpc::PooledClient;
use crate::write_consistency::{PeerRedisPool, SiteReplicationManager};
use crate::grpc::rustycluster::PingRequest;

/// Health check manager for maintaining connection health across all connection types
pub struct HealthCheckManager {
    // Redis connection health check
    redis_pool: Option<Arc<Pool>>,
    redis_keepalive_enabled: bool,
    redis_keepalive_interval: Duration,

    // Secondary nodes health check
    secondary_nodes: Vec<String>,
    secondary_nodes_keepalive_enabled: bool,
    secondary_nodes_keepalive_interval: Duration,

    // Site replication nodes health check
    site_replication_manager: Option<Arc<tokio::sync::RwLock<SiteReplicationManager>>>,
    site_nodes_keepalive_enabled: bool,
    site_nodes_keepalive_interval: Duration,

    // Peer Redis nodes health check
    peer_redis_pool: Option<Arc<PeerRedisPool>>,
    peer_redis_keepalive_enabled: bool,
    peer_redis_keepalive_interval: Duration,
}

impl HealthCheckManager {
    pub fn new(
        redis_pool: Option<Arc<Pool>>,
        redis_keepalive_enabled: bool,
        redis_keepalive_interval_secs: u64,
        secondary_nodes: Vec<String>,
        secondary_nodes_keepalive_enabled: bool,
        secondary_nodes_keepalive_interval_secs: u64,
        site_replication_manager: Option<Arc<tokio::sync::RwLock<SiteReplicationManager>>>,
        site_nodes_keepalive_enabled: bool,
        site_nodes_keepalive_interval_secs: u64,
        peer_redis_pool: Option<Arc<PeerRedisPool>>,
        peer_redis_keepalive_enabled: bool,
        peer_redis_keepalive_interval_secs: u64,
    ) -> Self {
        Self {
            redis_pool,
            redis_keepalive_enabled,
            redis_keepalive_interval: Duration::from_secs(redis_keepalive_interval_secs),
            secondary_nodes,
            secondary_nodes_keepalive_enabled,
            secondary_nodes_keepalive_interval: Duration::from_secs(secondary_nodes_keepalive_interval_secs),
            site_replication_manager,
            site_nodes_keepalive_enabled,
            site_nodes_keepalive_interval: Duration::from_secs(site_nodes_keepalive_interval_secs),
            peer_redis_pool,
            peer_redis_keepalive_enabled,
            peer_redis_keepalive_interval: Duration::from_secs(peer_redis_keepalive_interval_secs),
        }
    }

    /// Start all health check tasks
    pub fn start_health_checks(self: Arc<Self>) {
        info!("Starting health check manager");

        // Start Redis health check
        if self.redis_keepalive_enabled && self.redis_pool.is_some() {
            info!("Starting Redis health check task with interval: {:?}", self.redis_keepalive_interval);
            let manager_clone = self.clone();
            tokio::spawn(async move {
                manager_clone.redis_health_check_task().await;
            });
        }

        // Start secondary nodes health check
        if self.secondary_nodes_keepalive_enabled && !self.secondary_nodes.is_empty() {
            info!("Starting secondary nodes health check task with interval: {:?}", self.secondary_nodes_keepalive_interval);
            let manager_clone = self.clone();
            tokio::spawn(async move {
                manager_clone.secondary_nodes_health_check_task().await;
            });
        }

        // Start site replication nodes health check
        if self.site_nodes_keepalive_enabled && self.site_replication_manager.is_some() {
            info!("Starting site replication nodes health check task with interval: {:?}", self.site_nodes_keepalive_interval);
            let manager_clone = self.clone();
            tokio::spawn(async move {
                manager_clone.site_nodes_health_check_task().await;
            });
        }

        // Start peer Redis nodes health check
        if self.peer_redis_keepalive_enabled && self.peer_redis_pool.is_some() {
            info!("Starting peer Redis nodes health check task with interval: {:?}", self.peer_redis_keepalive_interval);
            let manager_clone = self.clone();
            tokio::spawn(async move {
                manager_clone.peer_redis_health_check_task().await;
            });
        }
    }

    /// Redis connection health check task
    async fn redis_health_check_task(&self) {
        let mut interval_timer = interval(self.redis_keepalive_interval);

        loop {
            interval_timer.tick().await;

            if let Some(pool) = &self.redis_pool {
                match self.ping_redis_connection(pool).await {
                    Ok(_) => {
                        debug!("Redis health check successful");
                    }
                    Err(e) => {
                        warn!("Redis health check failed: {}", e);
                    }
                }
            }
        }
    }

    /// Secondary nodes health check task
    async fn secondary_nodes_health_check_task(&self) {
        let mut interval_timer = interval(self.secondary_nodes_keepalive_interval);

        loop {
            interval_timer.tick().await;

            for node in &self.secondary_nodes {
                match self.ping_secondary_node(node).await {
                    Ok(_) => {
                        debug!("Secondary node {} health check successful", node);
                    }
                    Err(e) => {
                        warn!("Secondary node {} health check failed: {}", node, e);
                    }
                }
            }
        }
    }

    /// Site replication nodes health check task
    async fn site_nodes_health_check_task(&self) {
        let mut interval_timer = interval(self.site_nodes_keepalive_interval);

        loop {
            interval_timer.tick().await;

            if let Some(site_manager) = &self.site_replication_manager {
                match self.ping_site_replication_nodes(site_manager).await {
                    Ok(_) => {
                        debug!("Site replication nodes health check successful");
                    }
                    Err(e) => {
                        warn!("Site replication nodes health check failed: {}", e);
                    }
                }
            }
        }
    }

    /// Peer Redis nodes health check task
    async fn peer_redis_health_check_task(&self) {
        let mut interval_timer = interval(self.peer_redis_keepalive_interval);

        loop {
            interval_timer.tick().await;

            if let Some(peer_pool) = &self.peer_redis_pool {
                match self.ping_peer_redis_nodes(peer_pool).await {
                    Ok(_) => {
                        debug!("Peer Redis nodes health check successful");
                    }
                    Err(e) => {
                        warn!("Peer Redis nodes health check failed: {}", e);
                    }
                }
            }
        }
    }

    /// Ping Redis connection to keep it alive
    async fn ping_redis_connection(&self, pool: &Pool) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut conn = pool.get().await?;
        let _: String = deadpool_redis::redis::cmd("PING").query_async(&mut conn).await?;
        Ok(())
    }

    /// Ping secondary node to keep connection alive
    async fn ping_secondary_node(&self, node: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Create a temporary client for health check
        let mut pooled_client = PooledClient::new(1, node).await?;
        let mut client = pooled_client.next_client();

        let request = tonic::Request::new(PingRequest {});
        let _response = client.ping(request).await?;

        Ok(())
    }

    /// Ping site replication nodes to keep connections alive
    async fn ping_site_replication_nodes(&self, _site_manager: &Arc<tokio::sync::RwLock<SiteReplicationManager>>) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // This is a placeholder - the actual implementation would depend on
        // the SiteReplicationManager's interface for health checks
        debug!("Site replication nodes health check - placeholder implementation");
        Ok(())
    }

    /// Ping peer Redis nodes to keep connections alive
    async fn ping_peer_redis_nodes(&self, peer_pool: &PeerRedisPool) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Get pool statistics to verify connections are healthy
        let stats = peer_pool.get_pool_stats();

        for (node_url, (current_size, max_size)) in stats {
            debug!("Peer Redis node {} pool status: {}/{}", node_url, current_size, max_size);

            // Perform a simple ping operation to keep connections alive
            if peer_pool.ping_node(&node_url).await {
                debug!("Peer Redis node {} ping successful", node_url);
            } else {
                warn!("Peer Redis node {} ping failed", node_url);
            }
        }

        Ok(())
    }
}
