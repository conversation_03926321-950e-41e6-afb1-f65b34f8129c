#!/usr/bin/env python3
"""
Performance test script for optimized site replication configuration.
This script tests the performance improvements with the optimized configuration.
"""

import grpc
import asyncio
import time
import sys
import os
import threading
from concurrent.futures import ThreadPoolExecutor
import statistics

# Add the load directory to the path to import the generated protobuf files
sys.path.append(os.path.join(os.path.dirname(__file__), 'load'))

import rustycluster_pb2
import rustycluster_pb2_grpc

class PerformanceTest:
    def __init__(self, server_url="127.0.0.1:50051", username="testuser", password="testpass"):
        self.server_url = server_url
        self.username = username
        self.password = password
        self.token = None
        self.channel = None
        self.stub = None
        
    def connect(self):
        """Connect to the server and authenticate."""
        try:
            self.channel = grpc.insecure_channel(self.server_url)
            self.stub = rustycluster_pb2_grpc.KeyValueServiceStub(self.channel)
            
            # Authenticate
            auth_request = rustycluster_pb2.AuthenticateRequest(
                username=self.username,
                password=self.password
            )
            
            auth_response = self.stub.Authenticate(auth_request)
            if auth_response.success:
                self.token = auth_response.session_token
                print(f"✅ Connected and authenticated to {self.server_url}")
                return True
            else:
                print(f"❌ Authentication failed: {auth_response.message}")
                return False
                
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    def get_metadata(self):
        """Get authentication metadata."""
        if self.token:
            return [('authorization', f'Bearer {self.token}')]
        return []
    
    def single_set_operation(self, key, value):
        """Perform a single SET operation."""
        try:
            request = rustycluster_pb2.SetRequest(key=key, value=value)
            response = self.stub.Set(request, metadata=self.get_metadata())
            return response.success
        except Exception as e:
            print(f"SET operation failed: {e}")
            return False
    
    def batch_operations(self, operations):
        """Perform batch operations."""
        try:
            batch_ops = []
            for i, (op_type, key, value) in enumerate(operations):
                if op_type == "SET":
                    batch_op = rustycluster_pb2.BatchOperation(
                        operation_type=rustycluster_pb2.BatchOperation.SET,
                        key=key,
                        value=value
                    )
                elif op_type == "DELETE":
                    batch_op = rustycluster_pb2.BatchOperation(
                        operation_type=rustycluster_pb2.BatchOperation.DELETE,
                        key=key,
                        value=""
                    )
                batch_ops.append(batch_op)
            
            request = rustycluster_pb2.BatchWriteRequest(operations=batch_ops)
            response = self.stub.BatchWrite(request, metadata=self.get_metadata())
            return response.success
        except Exception as e:
            print(f"Batch operation failed: {e}")
            return False
    
    def performance_test_single_ops(self, num_operations=1000, num_threads=10):
        """Test performance with individual operations."""
        print(f"\n🚀 Testing {num_operations} individual SET operations with {num_threads} threads...")
        
        operations_per_thread = num_operations // num_threads
        results = []
        latencies = []
        
        def worker_thread(thread_id):
            thread_results = []
            thread_latencies = []
            
            for i in range(operations_per_thread):
                key = f"perf_test_{thread_id}_{i}"
                value = f"value_{thread_id}_{i}_{time.time()}"
                
                start_time = time.time()
                success = self.single_set_operation(key, value)
                end_time = time.time()
                
                thread_results.append(success)
                thread_latencies.append((end_time - start_time) * 1000)  # Convert to ms
            
            return thread_results, thread_latencies
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(worker_thread, i) for i in range(num_threads)]
            
            for future in futures:
                thread_results, thread_latencies = future.result()
                results.extend(thread_results)
                latencies.extend(thread_latencies)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        successful_ops = sum(results)
        tps = successful_ops / total_time
        
        print(f"📊 Individual Operations Results:")
        print(f"   Total Operations: {len(results)}")
        print(f"   Successful: {successful_ops}")
        print(f"   Failed: {len(results) - successful_ops}")
        print(f"   Total Time: {total_time:.2f}s")
        print(f"   TPS: {tps:.2f}")
        print(f"   Average Latency: {statistics.mean(latencies):.2f}ms")
        print(f"   P95 Latency: {statistics.quantiles(latencies, n=20)[18]:.2f}ms")
        print(f"   P99 Latency: {statistics.quantiles(latencies, n=100)[98]:.2f}ms")
        
        return tps, latencies
    
    def performance_test_batch_ops(self, num_batches=100, batch_size=100, num_threads=5):
        """Test performance with batch operations."""
        total_operations = num_batches * batch_size
        print(f"\n🚀 Testing {total_operations} operations in {num_batches} batches (size={batch_size}) with {num_threads} threads...")
        
        batches_per_thread = num_batches // num_threads
        results = []
        latencies = []
        
        def worker_thread(thread_id):
            thread_results = []
            thread_latencies = []
            
            for batch_id in range(batches_per_thread):
                # Create batch operations
                operations = []
                for i in range(batch_size):
                    key = f"batch_test_{thread_id}_{batch_id}_{i}"
                    value = f"batch_value_{thread_id}_{batch_id}_{i}_{time.time()}"
                    operations.append(("SET", key, value))
                
                start_time = time.time()
                success = self.batch_operations(operations)
                end_time = time.time()
                
                thread_results.append(success)
                thread_latencies.append((end_time - start_time) * 1000)  # Convert to ms
            
            return thread_results, thread_latencies
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(worker_thread, i) for i in range(num_threads)]
            
            for future in futures:
                thread_results, thread_latencies = future.result()
                results.extend(thread_results)
                latencies.extend(thread_latencies)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        successful_batches = sum(results)
        successful_ops = successful_batches * batch_size
        tps = successful_ops / total_time
        
        print(f"📊 Batch Operations Results:")
        print(f"   Total Batches: {len(results)}")
        print(f"   Successful Batches: {successful_batches}")
        print(f"   Total Operations: {successful_ops}")
        print(f"   Total Time: {total_time:.2f}s")
        print(f"   TPS: {tps:.2f}")
        print(f"   Average Batch Latency: {statistics.mean(latencies):.2f}ms")
        print(f"   P95 Batch Latency: {statistics.quantiles(latencies, n=20)[18]:.2f}ms")
        print(f"   P99 Batch Latency: {statistics.quantiles(latencies, n=100)[98]:.2f}ms")
        
        return tps, latencies
    
    def mixed_operations_test(self, num_operations=1000, num_threads=10):
        """Test with mixed operation types."""
        print(f"\n🚀 Testing {num_operations} mixed operations with {num_threads} threads...")
        
        operations_per_thread = num_operations // num_threads
        results = []
        latencies = []
        
        def worker_thread(thread_id):
            thread_results = []
            thread_latencies = []
            
            for i in range(operations_per_thread):
                # Mix of different operations
                if i % 4 == 0:
                    # SET operation
                    key = f"mixed_set_{thread_id}_{i}"
                    value = f"value_{thread_id}_{i}"
                    start_time = time.time()
                    success = self.single_set_operation(key, value)
                    end_time = time.time()
                elif i % 4 == 1:
                    # DELETE operation
                    key = f"mixed_set_{thread_id}_{i-1}"  # Delete previous key
                    try:
                        request = rustycluster_pb2.DeleteRequest(key=key)
                        start_time = time.time()
                        response = self.stub.Delete(request, metadata=self.get_metadata())
                        end_time = time.time()
                        success = response.success
                    except:
                        success = False
                        end_time = start_time
                elif i % 4 == 2:
                    # INCRBY operation
                    key = f"mixed_incr_{thread_id}_{i}"
                    try:
                        request = rustycluster_pb2.IncrByRequest(key=key, value=1)
                        start_time = time.time()
                        response = self.stub.IncrBy(request, metadata=self.get_metadata())
                        end_time = time.time()
                        success = True  # IncrBy returns new_value, not success
                    except:
                        success = False
                        end_time = start_time
                else:
                    # HSET operation
                    key = f"mixed_hash_{thread_id}_{i}"
                    try:
                        request = rustycluster_pb2.HSetRequest(
                            key=key, 
                            field="field1", 
                            value=f"hash_value_{i}"
                        )
                        start_time = time.time()
                        response = self.stub.HSet(request, metadata=self.get_metadata())
                        end_time = time.time()
                        success = response.success
                    except:
                        success = False
                        end_time = start_time
                
                thread_results.append(success)
                thread_latencies.append((end_time - start_time) * 1000)
            
            return thread_results, thread_latencies
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(worker_thread, i) for i in range(num_threads)]
            
            for future in futures:
                thread_results, thread_latencies = future.result()
                results.extend(thread_results)
                latencies.extend(thread_latencies)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        successful_ops = sum(results)
        tps = successful_ops / total_time
        
        print(f"📊 Mixed Operations Results:")
        print(f"   Total Operations: {len(results)}")
        print(f"   Successful: {successful_ops}")
        print(f"   Failed: {len(results) - successful_ops}")
        print(f"   Total Time: {total_time:.2f}s")
        print(f"   TPS: {tps:.2f}")
        print(f"   Average Latency: {statistics.mean(latencies):.2f}ms")
        print(f"   P95 Latency: {statistics.quantiles(latencies, n=20)[18]:.2f}ms")
        print(f"   P99 Latency: {statistics.quantiles(latencies, n=100)[98]:.2f}ms")
        
        return tps, latencies

def main():
    print("🔥 RustyCluster Optimized Performance Test")
    print("=" * 50)
    
    # Test configuration
    test = PerformanceTest()
    
    if not test.connect():
        print("❌ Failed to connect to server")
        return False
    
    print("\n📋 Test Configuration:")
    print(f"   Server: {test.server_url}")
    print(f"   Authentication: Enabled")
    print(f"   Site Replication: Enabled (check server config)")
    
    # Run performance tests
    results = {}
    
    # Test 1: Individual operations
    tps1, _ = test.performance_test_single_ops(num_operations=2000, num_threads=20)
    results['individual_ops'] = tps1
    
    # Test 2: Batch operations
    tps2, _ = test.performance_test_batch_ops(num_batches=50, batch_size=200, num_threads=10)
    results['batch_ops'] = tps2
    
    # Test 3: Mixed operations
    tps3, _ = test.mixed_operations_test(num_operations=1500, num_threads=15)
    results['mixed_ops'] = tps3
    
    # Summary
    print("\n" + "=" * 50)
    print("📈 PERFORMANCE SUMMARY")
    print("=" * 50)
    print(f"Individual Operations TPS: {results['individual_ops']:.2f}")
    print(f"Batch Operations TPS:      {results['batch_ops']:.2f}")
    print(f"Mixed Operations TPS:      {results['mixed_ops']:.2f}")
    print(f"Average TPS:               {statistics.mean(results.values()):.2f}")
    
    # Performance evaluation
    avg_tps = statistics.mean(results.values())
    if avg_tps >= 10000:
        print("\n🎉 EXCELLENT: Target performance achieved (10000+ TPS)!")
    elif avg_tps >= 8000:
        print("\n✅ GOOD: Significant improvement, close to target")
    elif avg_tps >= 7000:
        print("\n⚠️  MODERATE: Some improvement, but more optimization needed")
    else:
        print("\n❌ POOR: Limited improvement, major optimizations required")
    
    print(f"\nPerformance vs Target (10000 TPS): {(avg_tps/10000)*100:.1f}%")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
