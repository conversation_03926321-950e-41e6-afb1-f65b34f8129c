# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: rustycluster.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'rustycluster.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12rustycluster.proto\x12\x0crustycluster\"9\n\x13\x41uthenticateRequest\x12\x10\n\x08username\x18\x01 \x01(\t\x12\x10\n\x08password\x18\x02 \x01(\t\"O\n\x14\x41uthenticateResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rsession_token\x18\x02 \x01(\t\x12\x0f\n\x07message\x18\x03 \x01(\t\"B\n\nSetRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\x12\x18\n\x10skip_replication\x18\x03 \x01(\x08\"\x1e\n\x0bSetResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\x19\n\nGetRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\"+\n\x0bGetResponse\x12\r\n\x05value\x18\x01 \x01(\t\x12\r\n\x05\x66ound\x18\x02 \x01(\x08\"6\n\rDeleteRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x18\n\x10skip_replication\x18\x02 \x01(\x08\"!\n\x0e\x44\x65leteResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"Q\n\x0cSetExRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\x12\x0b\n\x03ttl\x18\x03 \x01(\x04\x12\x18\n\x10skip_replication\x18\x04 \x01(\x08\" \n\rSetExResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"F\n\x10SetExpiryRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x0b\n\x03ttl\x18\x02 \x01(\x03\x12\x18\n\x10skip_replication\x18\x03 \x01(\x08\"$\n\x11SetExpiryResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"E\n\rIncrByRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x03\x12\x18\n\x10skip_replication\x18\x03 \x01(\x08\"#\n\x0eIncrByResponse\x12\x11\n\tnew_value\x18\x01 \x01(\x03\"E\n\rDecrByRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x03\x12\x18\n\x10skip_replication\x18\x03 \x01(\x08\"#\n\x0e\x44\x65\x63rByResponse\x12\x11\n\tnew_value\x18\x01 \x01(\x03\"J\n\x12IncrByFloatRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01\x12\x18\n\x10skip_replication\x18\x03 \x01(\x08\"(\n\x13IncrByFloatResponse\x12\x11\n\tnew_value\x18\x01 \x01(\x01\"R\n\x0bHSetRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05\x66ield\x18\x02 \x01(\t\x12\r\n\x05value\x18\x03 \x01(\t\x12\x18\n\x10skip_replication\x18\x04 \x01(\x08\"\x1f\n\x0cHSetResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\")\n\x0bHGetRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05\x66ield\x18\x02 \x01(\t\",\n\x0cHGetResponse\x12\r\n\x05value\x18\x01 \x01(\t\x12\r\n\x05\x66ound\x18\x02 \x01(\x08\"\x1d\n\x0eHGetAllRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\"{\n\x0fHGetAllResponse\x12\x39\n\x06\x66ields\x18\x01 \x03(\x0b\x32).rustycluster.HGetAllResponse.FieldsEntry\x1a-\n\x0b\x46ieldsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"U\n\x0eHIncrByRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05\x66ield\x18\x02 \x01(\t\x12\r\n\x05value\x18\x03 \x01(\x03\x12\x18\n\x10skip_replication\x18\x04 \x01(\x08\"$\n\x0fHIncrByResponse\x12\x11\n\tnew_value\x18\x01 \x01(\x03\"U\n\x0eHDecrByRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05\x66ield\x18\x02 \x01(\t\x12\r\n\x05value\x18\x03 \x01(\x03\x12\x18\n\x10skip_replication\x18\x04 \x01(\x08\"$\n\x0fHDecrByResponse\x12\x11\n\tnew_value\x18\x01 \x01(\x03\"Z\n\x13HIncrByFloatRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05\x66ield\x18\x02 \x01(\t\x12\r\n\x05value\x18\x03 \x01(\x01\x12\x18\n\x10skip_replication\x18\x04 \x01(\x08\")\n\x14HIncrByFloatResponse\x12\x11\n\tnew_value\x18\x01 \x01(\x01\"\x98\x03\n\x0e\x42\x61tchOperation\x12\x42\n\x0eoperation_type\x18\x01 \x01(\x0e\x32*.rustycluster.BatchOperation.OperationType\x12\x0b\n\x03key\x18\x02 \x01(\t\x12\r\n\x05value\x18\x03 \x01(\t\x12\x12\n\x05\x66ield\x18\x04 \x01(\tH\x00\x88\x01\x01\x12\x16\n\tint_value\x18\x05 \x01(\x03H\x01\x88\x01\x01\x12\x18\n\x0b\x66loat_value\x18\x06 \x01(\x01H\x02\x88\x01\x01\x12\x10\n\x03ttl\x18\x07 \x01(\x04H\x03\x88\x01\x01\"\x9d\x01\n\rOperationType\x12\x07\n\x03SET\x10\x00\x12\n\n\x06\x44\x45LETE\x10\x01\x12\t\n\x05SETEX\x10\x02\x12\r\n\tSETEXPIRY\x10\x03\x12\n\n\x06INCRBY\x10\x04\x12\n\n\x06\x44\x45\x43RBY\x10\x05\x12\x0f\n\x0bINCRBYFLOAT\x10\x06\x12\x08\n\x04HSET\x10\x07\x12\x0b\n\x07HINCRBY\x10\x08\x12\x0b\n\x07HDECRBY\x10\t\x12\x10\n\x0cHINCRBYFLOAT\x10\nB\x08\n\x06_fieldB\x0c\n\n_int_valueB\x0e\n\x0c_float_valueB\x06\n\x04_ttl\"_\n\x11\x42\x61tchWriteRequest\x12\x30\n\noperations\x18\x01 \x03(\x0b\x32\x1c.rustycluster.BatchOperation\x12\x18\n\x10skip_replication\x18\x02 \x01(\x08\"@\n\x12\x42\x61tchWriteResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x19\n\x11operation_results\x18\x02 \x03(\x08\"\r\n\x0bPingRequest\"D\n\x0cPingResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x12\n\nlatency_ms\x18\x03 \x01(\x03\x32\xd0\t\n\x0fKeyValueService\x12U\n\x0c\x41uthenticate\x12!.rustycluster.AuthenticateRequest\x1a\".rustycluster.AuthenticateResponse\x12=\n\x04Ping\x12\x19.rustycluster.PingRequest\x1a\x1a.rustycluster.PingResponse\x12:\n\x03Set\x12\x18.rustycluster.SetRequest\x1a\x19.rustycluster.SetResponse\x12:\n\x03Get\x12\x18.rustycluster.GetRequest\x1a\x19.rustycluster.GetResponse\x12\x43\n\x06\x44\x65lete\x12\x1b.rustycluster.DeleteRequest\x1a\x1c.rustycluster.DeleteResponse\x12@\n\x05SetEx\x12\x1a.rustycluster.SetExRequest\x1a\x1b.rustycluster.SetExResponse\x12L\n\tSetExpiry\x12\x1e.rustycluster.SetExpiryRequest\x1a\x1f.rustycluster.SetExpiryResponse\x12\x43\n\x06IncrBy\x12\x1b.rustycluster.IncrByRequest\x1a\x1c.rustycluster.IncrByResponse\x12\x43\n\x06\x44\x65\x63rBy\x12\x1b.rustycluster.DecrByRequest\x1a\x1c.rustycluster.DecrByResponse\x12R\n\x0bIncrByFloat\x12 .rustycluster.IncrByFloatRequest\x1a!.rustycluster.IncrByFloatResponse\x12=\n\x04HSet\x12\x19.rustycluster.HSetRequest\x1a\x1a.rustycluster.HSetResponse\x12=\n\x04HGet\x12\x19.rustycluster.HGetRequest\x1a\x1a.rustycluster.HGetResponse\x12\x46\n\x07HGetAll\x12\x1c.rustycluster.HGetAllRequest\x1a\x1d.rustycluster.HGetAllResponse\x12\x46\n\x07HIncrBy\x12\x1c.rustycluster.HIncrByRequest\x1a\x1d.rustycluster.HIncrByResponse\x12\x46\n\x07HDecrBy\x12\x1c.rustycluster.HDecrByRequest\x1a\x1d.rustycluster.HDecrByResponse\x12U\n\x0cHIncrByFloat\x12!.rustycluster.HIncrByFloatRequest\x1a\".rustycluster.HIncrByFloatResponse\x12O\n\nBatchWrite\x12\x1f.rustycluster.BatchWriteRequest\x1a .rustycluster.BatchWriteResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'rustycluster_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_HGETALLRESPONSE_FIELDSENTRY']._loaded_options = None
  _globals['_HGETALLRESPONSE_FIELDSENTRY']._serialized_options = b'8\001'
  _globals['_AUTHENTICATEREQUEST']._serialized_start=36
  _globals['_AUTHENTICATEREQUEST']._serialized_end=93
  _globals['_AUTHENTICATERESPONSE']._serialized_start=95
  _globals['_AUTHENTICATERESPONSE']._serialized_end=174
  _globals['_SETREQUEST']._serialized_start=176
  _globals['_SETREQUEST']._serialized_end=242
  _globals['_SETRESPONSE']._serialized_start=244
  _globals['_SETRESPONSE']._serialized_end=274
  _globals['_GETREQUEST']._serialized_start=276
  _globals['_GETREQUEST']._serialized_end=301
  _globals['_GETRESPONSE']._serialized_start=303
  _globals['_GETRESPONSE']._serialized_end=346
  _globals['_DELETEREQUEST']._serialized_start=348
  _globals['_DELETEREQUEST']._serialized_end=402
  _globals['_DELETERESPONSE']._serialized_start=404
  _globals['_DELETERESPONSE']._serialized_end=437
  _globals['_SETEXREQUEST']._serialized_start=439
  _globals['_SETEXREQUEST']._serialized_end=520
  _globals['_SETEXRESPONSE']._serialized_start=522
  _globals['_SETEXRESPONSE']._serialized_end=554
  _globals['_SETEXPIRYREQUEST']._serialized_start=556
  _globals['_SETEXPIRYREQUEST']._serialized_end=626
  _globals['_SETEXPIRYRESPONSE']._serialized_start=628
  _globals['_SETEXPIRYRESPONSE']._serialized_end=664
  _globals['_INCRBYREQUEST']._serialized_start=666
  _globals['_INCRBYREQUEST']._serialized_end=735
  _globals['_INCRBYRESPONSE']._serialized_start=737
  _globals['_INCRBYRESPONSE']._serialized_end=772
  _globals['_DECRBYREQUEST']._serialized_start=774
  _globals['_DECRBYREQUEST']._serialized_end=843
  _globals['_DECRBYRESPONSE']._serialized_start=845
  _globals['_DECRBYRESPONSE']._serialized_end=880
  _globals['_INCRBYFLOATREQUEST']._serialized_start=882
  _globals['_INCRBYFLOATREQUEST']._serialized_end=956
  _globals['_INCRBYFLOATRESPONSE']._serialized_start=958
  _globals['_INCRBYFLOATRESPONSE']._serialized_end=998
  _globals['_HSETREQUEST']._serialized_start=1000
  _globals['_HSETREQUEST']._serialized_end=1082
  _globals['_HSETRESPONSE']._serialized_start=1084
  _globals['_HSETRESPONSE']._serialized_end=1115
  _globals['_HGETREQUEST']._serialized_start=1117
  _globals['_HGETREQUEST']._serialized_end=1158
  _globals['_HGETRESPONSE']._serialized_start=1160
  _globals['_HGETRESPONSE']._serialized_end=1204
  _globals['_HGETALLREQUEST']._serialized_start=1206
  _globals['_HGETALLREQUEST']._serialized_end=1235
  _globals['_HGETALLRESPONSE']._serialized_start=1237
  _globals['_HGETALLRESPONSE']._serialized_end=1360
  _globals['_HGETALLRESPONSE_FIELDSENTRY']._serialized_start=1315
  _globals['_HGETALLRESPONSE_FIELDSENTRY']._serialized_end=1360
  _globals['_HINCRBYREQUEST']._serialized_start=1362
  _globals['_HINCRBYREQUEST']._serialized_end=1447
  _globals['_HINCRBYRESPONSE']._serialized_start=1449
  _globals['_HINCRBYRESPONSE']._serialized_end=1485
  _globals['_HDECRBYREQUEST']._serialized_start=1487
  _globals['_HDECRBYREQUEST']._serialized_end=1572
  _globals['_HDECRBYRESPONSE']._serialized_start=1574
  _globals['_HDECRBYRESPONSE']._serialized_end=1610
  _globals['_HINCRBYFLOATREQUEST']._serialized_start=1612
  _globals['_HINCRBYFLOATREQUEST']._serialized_end=1702
  _globals['_HINCRBYFLOATRESPONSE']._serialized_start=1704
  _globals['_HINCRBYFLOATRESPONSE']._serialized_end=1745
  _globals['_BATCHOPERATION']._serialized_start=1748
  _globals['_BATCHOPERATION']._serialized_end=2156
  _globals['_BATCHOPERATION_OPERATIONTYPE']._serialized_start=1951
  _globals['_BATCHOPERATION_OPERATIONTYPE']._serialized_end=2108
  _globals['_BATCHWRITEREQUEST']._serialized_start=2158
  _globals['_BATCHWRITEREQUEST']._serialized_end=2253
  _globals['_BATCHWRITERESPONSE']._serialized_start=2255
  _globals['_BATCHWRITERESPONSE']._serialized_end=2319
  _globals['_PINGREQUEST']._serialized_start=2321
  _globals['_PINGREQUEST']._serialized_end=2334
  _globals['_PINGRESPONSE']._serialized_start=2336
  _globals['_PINGRESPONSE']._serialized_end=2404
  _globals['_KEYVALUESERVICE']._serialized_start=2407
  _globals['_KEYVALUESERVICE']._serialized_end=3639
# @@protoc_insertion_point(module_scope)
