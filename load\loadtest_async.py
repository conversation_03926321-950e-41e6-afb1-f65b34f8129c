#!/usr/bin/env python3
"""
Ultra-high performance async load testing script for RustyCluster.
This version uses asyncio and aiogrpc for maximum throughput.

Requirements:
- pip install grpcio grpcio-tools aiogrpc asyncio
"""

import grpc
import asyncio
import time
import sys
import random
import string
from typing import List, Tuple
import statistics

try:
    import rustycluster_pb2
    import rustycluster_pb2_grpc
except ImportError:
    print("Error: gRPC Python stubs not found.")
    print("Please generate them first with:")
    print("python -m grpc_tools.protoc -I./proto --python_out=. --grpc_python_out=. proto/rustycluster.proto")
    sys.exit(1)

class AsyncLoadTester:
    def __init__(self, host="localhost", port=50051, username="testuser", password="testpass"):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.session_token = None
        self.channels = []
        self.stubs = []
        
    async def authenticate(self):
        """Authenticate and get session token"""
        channel = grpc.aio.insecure_channel(f'{self.host}:{self.port}')
        stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
        
        auth_request = rustycluster_pb2.AuthenticateRequest(
            username=self.username,
            password=self.password
        )
        
        try:
            auth_response = await stub.Authenticate(auth_request)
            
            if auth_response.success:
                self.session_token = auth_response.session_token
                print(f"✓ Authenticated successfully")
                await channel.close()
                return True
            else:
                print(f"✗ Authentication failed: {auth_response.message}")
                await channel.close()
                return False
        except Exception as e:
            print(f"✗ Authentication error: {e}")
            await channel.close()
            return False
    
    async def create_connection_pool(self, pool_size=100):
        """Create async connection pool"""
        print(f"Creating async connection pool with {pool_size} connections...")
        
        for i in range(pool_size):
            channel = grpc.aio.insecure_channel(f'{self.host}:{self.port}', options=[
                ('grpc.keepalive_time_ms', 10000),
                ('grpc.keepalive_timeout_ms', 5000),
                ('grpc.keepalive_permit_without_calls', True),
                ('grpc.http2.max_pings_without_data', 0),
                ('grpc.http2.min_time_between_pings_ms', 10000),
                ('grpc.http2.min_ping_interval_without_data_ms', 300000),
                ('grpc.max_send_message_length', 4 * 1024 * 1024),
                ('grpc.max_receive_message_length', 4 * 1024 * 1024),
            ])
            
            stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
            self.channels.append(channel)
            self.stubs.append(stub)
        
        print(f"✓ Created {len(self.stubs)} async connections")
    
    def generate_test_data(self, count):
        """Pre-generate test data"""
        print(f"Generating {count} test data entries...")
        data = []
        for i in range(count):
            key = f"loadtest_key_{i}_{random.randint(1000, 9999)}"
            value = f"loadtest_value_{i}_{''.join(random.choices(string.ascii_letters + string.digits, k=50))}"
            data.append((key, value))
        return data
    
    async def single_operation(self, stub, key, value, metadata, semaphore):
        """Perform a single Set operation with rate limiting"""
        async with semaphore:
            start_time = time.time()
            try:
                request = rustycluster_pb2.SetRequest(
                    key=key,
                    value=value,
                    skip_replication=False
                )
                
                response = await stub.Set(request, metadata=metadata, timeout=10.0)
                end_time = time.time()
                
                return {
                    'success': response.success,
                    'latency': (end_time - start_time) * 1000,
                    'error': None
                }
            except Exception as e:
                end_time = time.time()
                return {
                    'success': False,
                    'latency': (end_time - start_time) * 1000,
                    'error': str(e)
                }
    
    async def batch_worker(self, worker_id, operations_data, metadata, semaphore, results):
        """Process a batch of operations"""
        stub = self.stubs[worker_id % len(self.stubs)]
        
        tasks = []
        for key, value in operations_data:
            task = self.single_operation(stub, key, value, metadata, semaphore)
            tasks.append(task)
        
        # Execute all operations for this worker
        worker_results = await asyncio.gather(*tasks, return_exceptions=True)
        results.extend(worker_results)
    
    async def load_test(self, num_operations=200000, concurrency=1000, target_rps=10000, connection_pool_size=100):
        """Perform ultra-high performance async load testing"""
        if not await self.authenticate():
            return
        
        # Create connection pool
        await self.create_connection_pool(connection_pool_size)
        
        # Pre-generate test data
        test_data = self.generate_test_data(num_operations)
        
        print(f"Starting async load test:")
        print(f"  Operations: {num_operations}")
        print(f"  Concurrency: {concurrency}")
        print(f"  Target RPS: {target_rps}")
        print(f"  Connection Pool: {connection_pool_size}")
        
        # Create metadata with session token
        metadata = [('authorization', f'Bearer {self.session_token}')]
        
        # Create semaphore for rate limiting
        semaphore = asyncio.Semaphore(concurrency)
        
        # Split data into batches for workers
        batch_size = max(1, num_operations // connection_pool_size)
        batches = [test_data[i:i + batch_size] for i in range(0, len(test_data), batch_size)]
        
        print(f"Split into {len(batches)} batches of ~{batch_size} operations each")
        
        results = []
        start_time = time.time()
        
        # Create worker tasks
        worker_tasks = []
        for worker_id, batch in enumerate(batches):
            if batch:  # Only create task if batch is not empty
                task = self.batch_worker(worker_id, batch, metadata, semaphore, results)
                worker_tasks.append(task)
        
        # Execute all workers concurrently
        print(f"Starting {len(worker_tasks)} async workers...")
        await asyncio.gather(*worker_tasks)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Analyze results
        successful_ops = sum(1 for r in results if isinstance(r, dict) and r.get('success', False))
        failed_ops = len(results) - successful_ops
        
        latencies = [r['latency'] for r in results if isinstance(r, dict) and 'latency' in r]
        
        actual_rps = len(results) / duration
        avg_latency = statistics.mean(latencies) if latencies else 0
        p95_latency = statistics.quantiles(latencies, n=20)[18] if len(latencies) > 20 else 0
        p99_latency = statistics.quantiles(latencies, n=100)[98] if len(latencies) > 100 else 0
        
        print(f"\n{'='*60}")
        print(f"Async Load Test Results:")
        print(f"{'='*60}")
        print(f"Duration: {duration:.2f} seconds")
        print(f"Total operations: {len(results)}")
        print(f"Successful operations: {successful_ops}")
        print(f"Failed operations: {failed_ops}")
        print(f"Success rate: {(successful_ops / len(results)) * 100:.2f}%")
        print(f"")
        print(f"Performance Metrics:")
        print(f"  Actual RPS: {actual_rps:.2f}")
        print(f"  Target RPS: {target_rps}")
        print(f"  Performance: {(actual_rps / target_rps) * 100:.1f}% of target")
        print(f"")
        print(f"Latency Metrics:")
        print(f"  Average: {avg_latency:.2f}ms")
        print(f"  P95: {p95_latency:.2f}ms")
        print(f"  P99: {p99_latency:.2f}ms")
        print(f"{'='*60}")
        
        # Performance recommendations
        if actual_rps < target_rps * 0.8:
            print(f"\n⚠️  Performance below target")
            print("Optimization suggestions:")
            print("- Increase connection pool size (--pool-size)")
            print("- Increase concurrency (--concurrency)")
            print("- Check RustyCluster server resources")
            print("- Consider multiple client machines")
            print("- Verify network bandwidth")
        elif actual_rps >= target_rps:
            print(f"\n✅ Target performance achieved!")
        else:
            print(f"\n📊 Performance close to target")
    
    async def cleanup(self):
        """Clean up async resources"""
        for channel in self.channels:
            await channel.close()

async def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Ultra-high performance async load test')
    parser.add_argument('--host', default='localhost', help='RustyCluster host')
    parser.add_argument('--port', type=int, default=50051, help='RustyCluster port')
    parser.add_argument('--username', default='testuser', help='Username')
    parser.add_argument('--password', default='testpass', help='Password')
    parser.add_argument('--operations', '-n', type=int, default=200000, help='Number of operations')
    parser.add_argument('--concurrency', '-c', type=int, default=1000, help='Concurrent operations')
    parser.add_argument('--rps', type=int, default=10000, help='Target requests per second')
    parser.add_argument('--pool-size', type=int, default=100, help='Connection pool size')
    
    args = parser.parse_args()
    
    tester = AsyncLoadTester(args.host, args.port, args.username, args.password)
    
    try:
        await tester.load_test(args.operations, args.concurrency, args.rps, args.pool_size)
    finally:
        await tester.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
