# RustyCluster

RustyCluster is a distributed key-value store built in Rust, providing high availability and data consistency through replication. It uses Redis as the underlying storage engine and gRPC for inter-node communication.

## Features

- **Distributed Architecture**: Multiple nodes working together to provide high availability
- **Data Replication**: Automatic replication of data to secondary nodes
- **Configurable Consistency**: Adjustable read consistency levels
- **Connection Pooling**: Efficient connection management for both Redis and inter-node communication
- **Asynchronous Operations**: Support for both synchronous and asynchronous replication
- **Rich Data Types**: Support for strings, hashes, and numeric operations
- **TTL Support**: Built-in support for key expiration

## Architecture

RustyCluster consists of multiple nodes, where:
- Each node can be either a primary or secondary node
- Primary nodes handle client requests and replicate data to secondary nodes
- Secondary nodes maintain copies of the data and can serve read requests
- All nodes communicate via gRPC
- Redis is used as the underlying storage engine

## Prerequisites

- Rust (latest stable version)
- Redis server
- Protobuf compiler (for gRPC)


## Configuration

RustyCluster uses TOML configuration files. A sample configuration file (`config.toml`) looks like this:

# Redis connection URL for the primary node
redis_url = "redis://127.0.0.1:6379"

# List of secondary node URLs that this node will replicate to
secondary_nodes = ["http://127.0.0.1:50052", "http://127.0.0.1:50053"]

# Port number on which the gRPC server will listen
port = 50051

# Maximum number of connections in the Redis connection pool
redis_pool_size = 512

# Number of secondary nodes to which data should be replicated
replication_factor = 2

# Number of secondary nodes that must have the data before considering it consistent
read_consistency = 0

# If true, replication to secondary nodes happens asynchronously
async_replication = true

# Maximum number of retry attempts when connecting to secondary nodes
max_retries = 3

# Delay in milliseconds between retry attempts
retry_delay_ms = 100

# Maximum age in seconds for a replication batch
replication_batch_max_age_secs = 30

# Maximum number of connections per secondary node in the gRPC client pool
secondary_pool_size = 512

## API Reference

### String Operations
- `Set(key, value)`: Set a key-value pair
- `Get(key)`: Retrieve a value by key
- `Delete(key)`: Delete a key
- `SetEx(key, value, ttl)`: Set a key-value pair with expiration
- `SetExpiry(key, ttl)`: Set expiration for an existing key

### Numeric Operations
- `IncrBy(key, value)`: Increment a numeric value
- `DecrBy(key, value)`: Decrement a numeric value
- `IncrByFloat(key, value)`: Increment a floating-point value

### Hash Operations
- `HSet(key, field, value)`: Set a field in a hash
- `HGet(key, field)`: Get a field from a hash
- `HGetAll(key)`: Get all fields from a hash
- `HIncrBy(key, field, value)`: Increment a numeric field
- `HDecrBy(key, field, value)`: Decrement a numeric field
- `HIncrByFloat(key, field, value)`: Increment a floating-point field

## Performance Considerations

- **Connection Pooling**: The application uses connection pooling for both Redis and inter-node communication to improve performance
- **Asynchronous Replication**: When enabled, replication happens asynchronously to avoid blocking client operations
- **Batch Processing**: Operations are batched for efficient replication
- **Configurable Pool Sizes**: Both Redis and secondary node connection pool sizes can be configured based on your needs

## Error Handling

- Automatic retries for failed operations
- Configurable retry count and delay
- Detailed error logging
- Graceful handling of node failures

## Perofmance expectations
We need to achieve 10000+ rps