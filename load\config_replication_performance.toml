# Ultra-high performance configuration for Rusty<PERSON><PERSON> with replication_factor = 2
# Optimized to achieve 10K+ RPS with full replication

# Redis connection URL for the primary node
redis_url = "redis://settlenxt:npci@127.0.0.1:6379"

# List of secondary node URLs that this node will replicate to
secondary_nodes = ["http://127.0.0.1:50052", "http://127.0.0.1:50053"]

# Port number on which the gRPC server will listen
port = 50051

# Maximize Redis connection pool for high throughput
redis_pool_size = 4096

# REPLICATION SETTINGS - Optimized for 10K+ RPS
replication_factor = 2
read_consistency = 0
async_replication = true

# Optimize retry settings for high-performance replication
max_retries = 2
retry_delay_ms = 5

# Maximize secondary node connections for parallel replication
secondary_pool_size = 2048

# Optimize batching for maximum replication throughput
max_batch_size = 200000
batch_flush_interval_ms = 5
replication_batch_max_age_secs = 60

# Optimize network settings for maximum performance
tcp_keepalive_secs = 5
tcp_nodelay = true
concurrency_limit = 4096
max_concurrent_streams = 16384

# Optimize batch processing for replication
chunk_size = 100000
num_shards = 512

# Maximize worker threads for high concurrency
worker_threads = 64

# Authentication disabled for maximum performance testing
auth_enabled = false
auth_username = ""
auth_password = ""
session_duration_secs = 3600
